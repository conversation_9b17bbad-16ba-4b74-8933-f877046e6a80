﻿/**
 * Extracted from: grind.html
 * Generated: 2025-06-09 13:05:31
 * Type: Regular Script
 */

// Backend compatibility layer
if (typeof window === 'undefined') {
    global.window = global.window || {};
    global.document = global.document || {
        addEventListener: function() {},
        getElementById: function() { return { style: {}, innerHTML: '', textContent: '' }; },
        createElement: function() { return { style: {}, setAttribute: function() {}, appendChild: function() {} }; },
        querySelector: function() { return null; },
        querySelectorAll: function() { return []; },
        body: { appendChild: function() {}, style: {} },
        head: { appendChild: function() {} }
    };
    global.console = console;
}

// Original script content:
// Add these at the top of your document, just like you did with interleaveTask

  // Complete Task Function
  window.completeTask = function(projectId, taskId) {
    try {
      // Get the priority tasks list
      const priorityTasks = JSON.parse(localStorage.getItem('calculatedPriorityTasks') || '[]');

      // Find and remove the completed task
      const taskIndex = priorityTasks.findIndex(task =>
        (task.id === taskId || task.id === String(taskId)) &&
        (task.projectId === projectId || task.projectId === String(projectId))
      );

      if (taskIndex === -1) {
        console.error('Task not found:', taskId, projectId);
        alert('Task not found. Please try again or refresh the page.');
        return;
      }

      const completedTask = priorityTasks.splice(taskIndex, 1)[0];

      // Save updated priority tasks list
      localStorage.setItem('calculatedPriorityTasks', JSON.stringify(priorityTasks));

      // Optional: Add to completed tasks list
      const completedTasks = JSON.parse(localStorage.getItem('completedTasks') || '[]');
      completedTask.completedDate = new Date().toISOString();
      completedTasks.push(completedTask);
      localStorage.setItem('completedTasks', JSON.stringify(completedTasks));

      // Remove the original task from its project's task list
      if (projectId) {
        const projectTasks = JSON.parse(localStorage.getItem(`tasks-${projectId}`) || '[]');
        const projTaskIndex = projectTasks.findIndex(t => t.id === taskId);

        if (projTaskIndex !== -1) {
          projectTasks.splice(projTaskIndex, 1);
          localStorage.setItem(`tasks-${projectId}`, JSON.stringify(projectTasks));

          // If Firestore is available, save the updated tasks
          if (typeof window.saveTasksToFirestore === 'function') {
            window.saveTasksToFirestore(projectId, projectTasks).catch(error => {
              console.error('Error saving completed task to Firestore:', error);
            });
          }
        }
      }

      // Show completion message
      const notification = document.createElement('div');
      notification.className = 'completion-notification';
      notification.innerHTML = `
        <div style="display: flex; align-items: center; gap: 10px;">
          <i class="bi bi-check-circle-fill" style="font-size: 1.5rem; color: var(--secondary-color);"></i>
          <div>
            <strong>Task Completed!</strong>
            <div>${completedTask.title}</div>
          </div>
        </div>
      `;
      notification.style.position = 'fixed';
      notification.style.bottom = '80px';
      notification.style.right = '20px';
      notification.style.backgroundColor = 'var(--card-bg)';
      notification.style.color = 'var(--text-color)';
      notification.style.padding = '15px';
      notification.style.borderRadius = '5px';
      notification.style.boxShadow = '0 3px 15px rgba(0,0,0,0.3)';
      notification.style.border = '1px solid var(--secondary-color)';
      notification.style.zIndex = '1000';
      notification.style.opacity = '0';
      notification.style.transition = 'opacity 0.3s ease, transform 0.3s ease';
      notification.style.transform = 'translateY(20px)';

      document.body.appendChild(notification);

      // Animate in and out
      setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateY(0)';
      }, 10);
      setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateY(20px)';
      }, 3000);
      setTimeout(() => { document.body.removeChild(notification); }, 3500);

      // Display the next task (force refresh since we know a task was removed)
      displayPriorityTask(true);

    } catch (error) {
      console.error('Error completing task:', error);
      alert('Error completing task: ' + error.message);
    }
  };

  // Subtasks Toggle Function
  window.toggleSubtasks = async function(button, taskId) {
    console.group(`Toggle Subtasks for Task ID: ${taskId}`);

    try {
      // Find the specific container for this task
      const container = document.getElementById(`subtasks-${taskId}`);

      if (!container) {
        console.error(`No container found for task ID: ${taskId}`);
        console.groupEnd();
        return;
      }

      const spinner = container.querySelector('.loading-spinner');
      const subtasksList = container.querySelector('.subtasks-list');

      // Toggle expanded state
      container.classList.toggle('expanded');
      if (button.classList) button.classList.toggle('expanded');

      // If we're expanding and there are no subtasks yet, generate them
      if (container.classList.contains('expanded')) {
        if (!subtasksList.children.length) {
          try {
            if (spinner) spinner.classList.remove('d-none');
            const tasks = JSON.parse(localStorage.getItem('calculatedPriorityTasks') || '[]');

            // Find task by ID or index
            const taskData = tasks.find((t) => String(t.id) === String(taskId));

            if (!taskData) {
              throw new Error(`Task not found for ID: ${taskId}`);
            }

            const subtasks = await generateSubtasks(taskId, taskData);

            subtasksList.innerHTML = subtasks.map((subtask, index) => `
              <div class="subtask-item" data-subtask-id="${taskId}-${index}">
                <input type="checkbox" class="subtask-checkbox" onchange="toggleSubtaskComplete(this)">
                <div class="subtask-title">${subtask}</div>
              </div>
            `).join('');

            // Load any previously saved completion status
            loadCompletionStatus();
          } catch (error) {
            console.error('Error in toggleSubtasks:', error);
            subtasksList.innerHTML = `
              <div class="subtask-error">
                <i class="bi bi-exclamation-triangle"></i>
                ${error.message}
              </div>
            `;
          } finally {
            if (spinner) spinner.classList.add('d-none');
          }
        }
      }
    } catch (error) {
      console.error('Error toggling subtasks:', error);
    }

    console.groupEnd();
  };

  // Generate Subtasks Function
  window.generateSubtasks = async function(taskId, taskData) {
    const prompt = `Break down this academic task into specific, actionable steps that require minimal decision-making:
  Task: ${taskData.title}
  Section: ${taskData.section}
  Project: ${taskData.projectName}
  Priority Score: ${taskData.priorityScore}

  Generate a numbered list of specific steps to complete this task. Each step should:
  1. Be concrete and actionable (start with verbs)
  2. Focus on a single, clear action
  3. Require minimal decision making
  4. Include any necessary preparation
  5. Build towards the final goal

  Important: Do not include any time estimates or duration information.

  Example format:
  1. Gather textbook and class notes
  2. Review chapter introduction
  3. Create main topic outline
  etc.`;

    try {
      console.log('Sending request with task data:', taskData);
      const response = await fetch('/api/generate-subtasks', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          prompt
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.details || 'Failed to generate subtasks');
      }

      const data = await response.json();
      console.log('Received subtasks:', data);

      if (!data.subtasks || data.subtasks.length === 0) {
        throw new Error('No subtasks were generated');
      }

      return data.subtasks;
    } catch (error) {
      console.error('Error generating subtasks:', error);
      return [`Error: ${error.message}. Please try again.`];
    }
  };

  // Toggle Subtask Complete Function
  window.toggleSubtaskComplete = function(checkbox) {
    const subtaskItem = checkbox.closest('.subtask-item');
    subtaskItem.classList.toggle('completed', checkbox.checked);

    // Save completion status
    const subtaskId = subtaskItem.dataset.subtaskId;
    const completedSubtasks = JSON.parse(localStorage.getItem('completedSubtasks') || '{}');
    completedSubtasks[subtaskId] = checkbox.checked;
    localStorage.setItem('completedSubtasks', JSON.stringify(completedSubtasks));
  };

  // Load Completion Status Function
  window.loadCompletionStatus = function() {
    const completedSubtasks = JSON.parse(localStorage.getItem('completedSubtasks') || '{}');
    Object.entries(completedSubtasks).forEach(([subtaskId, completed]) => {
      const subtaskItem = document.querySelector(`[data-subtask-id="${subtaskId}"]`);
      if (subtaskItem) {
        const checkbox = subtaskItem.querySelector('.subtask-checkbox');
        checkbox.checked = completed;
        subtaskItem.classList.toggle('completed', completed);
      }
    });
  };

  // Skip Task Function (if needed)
  window.skipTask = function() {
    try {
      const priorityTasks = JSON.parse(localStorage.getItem('calculatedPriorityTasks') || '[]');
      if (priorityTasks.length <= 1) return;

      // Move first task to the end (without marking as interleaved)
      const firstTask = priorityTasks.shift();
      priorityTasks.push(firstTask);

      // Save to localStorage
      localStorage.setItem('calculatedPriorityTasks', JSON.stringify(priorityTasks));

      // Display the next task (force refresh since we know the order changed)
      displayPriorityTask(true);

      // Show skip message
      const notification = document.createElement('div');
      notification.className = 'skip-notification';
      notification.innerHTML = `
        <div style="display: flex; align-items: center; gap: 10px;">
          <i class="bi bi-skip-forward-fill" style="font-size: 1.2rem;"></i>
          <div>
            <strong>Task Skipped</strong>
            <div>${firstTask.title}</div>
          </div>
        </div>
      `;
      notification.style.position = 'fixed';
      notification.style.bottom = '80px';
      notification.style.right = '20px';
      notification.style.backgroundColor = 'var(--card-bg)';
      notification.style.color = 'var(--text-color)';
      notification.style.padding = '12px 15px';
      notification.style.borderRadius = '5px';
      notification.style.boxShadow = '0 2px 10px rgba(0,0,0,0.2)';
      notification.style.zIndex = '1000';
      notification.style.opacity = '0';
      notification.style.transition = 'opacity 0.3s ease';

      document.body.appendChild(notification);

      // Fade in and out
      setTimeout(() => { notification.style.opacity = '1'; }, 10);
      setTimeout(() => { notification.style.opacity = '0'; }, 2000);
      setTimeout(() => { document.body.removeChild(notification); }, 2500);

    } catch (error) {
      console.error('Error in skipTask:', error);
    }
  };
