﻿/**
 * Extracted from: grind.html
 * Generated: 2025-06-09 13:05:32
 * Type: Regular Script
 */

// Backend compatibility layer
if (typeof window === 'undefined') {
    global.window = global.window || {};
    global.document = global.document || {
        addEventListener: function() {},
        getElementById: function() { return { style: {}, innerHTML: '', textContent: '' }; },
        createElement: function() { return { style: {}, setAttribute: function() {}, appendChild: function() {} }; },
        querySelector: function() { return null; },
        querySelectorAll: function() { return []; },
        body: { appendChild: function() {}, style: {} },
        head: { appendChild: function() {} }
    };
    global.console = console;
}

// Original script content:
// Files to upload storage
    let modalFilesToUpload = [];
    // DOM event listeners
    document.addEventListener('DOMContentLoaded', function() {
      displayPriorityTask();
      // Set up task modal file upload
      const taskModal = document.getElementById('taskModal');
      if (taskModal) {
        const uploadArea = document.getElementById('modalFileUploadArea');
        const fileInput = document.getElementById('modalFileUploadInput');
        const previewContainer = document.getElementById('modalUploadPreview');
        if (uploadArea && fileInput) {
          // Click to select files
          uploadArea.addEventListener('click', () => {
            fileInput.click();
          });
          // File input change handler
          fileInput.addEventListener('change', () => {
            const files = fileInput.files;
            if (files.length > 0) {
              addFilesToUploadPreview(files);
            }
          });
          // Drag and drop handlers
          uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('drag-over');
          });
          uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('drag-over');
          });
          uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('drag-over');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
              addFilesToUploadPreview(files);
            }
          });
        }
      }
    });
    // Add files to upload preview
    function addFilesToUploadPreview(files) {
      const previewContainer = document.getElementById('modalUploadPreview');
      if (!previewContainer) return;
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        modalFilesToUpload.push(file);
        // Create preview item
        const previewItem = document.createElement('div');
        previewItem.className = 'upload-preview-item';
        previewItem.dataset.index = modalFilesToUpload.length - 1;
        // Determine icon based on file type
        let iconClass = 'fas fa-file';
        if (file.type.startsWith('image/')) {
          iconClass = 'fas fa-file-image';
        } else if (file.type === 'application/pdf') {
          iconClass = 'fas fa-file-pdf';
        } else if (file.type.includes('spreadsheet') || file.type.includes('excel')) {
          iconClass = 'fas fa-file-excel';
        } else if (file.type.includes('document') || file.type.includes('word')) {
          iconClass = 'fas fa-file-word';
        } else if (file.type.includes('presentation') || file.type.includes('powerpoint')) {
          iconClass = 'fas fa-file-powerpoint';
        }
        previewItem.innerHTML = `
                    <div class="upload-preview-icon">
                        <i class="${iconClass}"></i>
                    </div>
                    <div class="upload-preview-name" title="${file.name}">${file.name}</div>
                    <div class="upload-preview-remove" data-index="${modalFilesToUpload.length - 1}">
                        <i class="fas fa-times"></i>
                    </div>
                `;
        previewContainer.appendChild(previewItem);
        // Add remove handler
        const removeButton = previewItem.querySelector('.upload-preview-remove');
        removeButton.addEventListener('click', (e) => {
          const index = parseInt(e.currentTarget.dataset.index);
          modalFilesToUpload[index] = null; // Mark as removed but keep array indices
          previewItem.remove();
        });
      }
    }
    // Clear upload preview when hiding modal
    function hideTaskModal() {
      const taskModal = document.getElementById('taskModal');
      taskModal.style.display = 'none';
      document.body.classList.remove('modal-open');
      document.body.style.overflow = ''; // Reset overflow
      document.body.style.paddingRight = ''; // Reset padding
      // Clear file upload previews
      const previewContainer = document.getElementById('modalUploadPreview');
      if (previewContainer) {
        previewContainer.innerHTML = '';
        modalFilesToUpload = [];
      }
    }
    // Upload files for task
    async function uploadFilesForTask(taskId) {
      if (!modalFilesToUpload.length) return;
      // Filter out removed files
      const filesToUpload = modalFilesToUpload.filter(file => file !== null);
      if (!filesToUpload.length) return;
      try {
        // Upload each file
        for (const file of filesToUpload) {
          await window.googleDriveAPI.uploadFile(file, taskId);
        }
        console.log('Files uploaded successfully for task:', taskId);
        return true;
      } catch (error) {
        console.error('Error uploading files for task:', error);
        return false;
      }
    }
