# PowerShell Script to Extract and Modularize Inline Scripts from HTML Files
# This script will:
# 1. Find all HTML files in the project
# 2. Extract inline JavaScript code from <script> tags (without src attribute)
# 3. Create separate .js files for the extracted code
# 4. Replace inline scripts with external script references
# 5. Organize scripts by functionality and ensure they can run in backend

param(
    [switch]$DryRun = $false,
    [switch]$Verbose = $false
)

# Configuration
$ScriptRoot = $PSScriptRoot
$ExtractedScriptsDir = Join-Path $ScriptRoot "js\extracted"
$BackupDir = Join-Path $ScriptRoot "backup-before-extraction"

# Ensure directories exist
if (-not (Test-Path $ExtractedScriptsDir)) {
    New-Item -ItemType Directory -Path $ExtractedScriptsDir -Force | Out-Null
}

if (-not (Test-Path $BackupDir)) {
    New-Item -ItemType Directory -Path $BackupDir -Force | Out-Null
}

# List of HTML files to process
$HtmlFiles = @(
    'index.html',
    'grind.html', 
    'academic-details.html',
    'workspace.html',
    'extracted.html',
    'daily-calendar.html',
    'study-spaces.html',
    'subject-marks.html',
    'settings.html',
    'tasks.html',
    'landing.html',
    'sleep-saboteurs.html',
    '404.html',
    'priority-list.html',
    'priority-calculator.html',
    'flashcards.html',
    'instant-test-feedback.html'
)

# Function to write verbose output
function Write-VerboseOutput {
    param([string]$Message)
    if ($Verbose) {
        Write-Host $Message -ForegroundColor Yellow
    }
}

# Function to create backup of HTML file
function Backup-HtmlFile {
    param([string]$FilePath)
    
    $fileName = Split-Path $FilePath -Leaf
    $backupPath = Join-Path $BackupDir "$fileName.backup-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
    
    if (Test-Path $FilePath) {
        Copy-Item $FilePath $backupPath
        Write-VerboseOutput "Backed up $fileName to $backupPath"
        return $backupPath
    }
    return $null
}

# Function to extract inline scripts from HTML content
function Extract-InlineScripts {
    param(
        [string]$HtmlContent,
        [string]$FileName
    )
    
    $extractedScripts = @()
    $scriptCounter = 1
    
    # Regex to match script tags without src attribute (inline scripts)
    $inlineScriptPattern = '(?s)<script(?![^>]*\bsrc\s*=)([^>]*)>(.*?)</script>'
    $matches = [regex]::Matches($HtmlContent, $inlineScriptPattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
    
    foreach ($match in $matches) {
        $attributes = $match.Groups[1].Value.Trim()
        $scriptContent = $match.Groups[2].Value.Trim()
        
        # Skip empty scripts or scripts with only whitespace/comments
        if ([string]::IsNullOrWhiteSpace($scriptContent) -or $scriptContent -match '^\s*//.*$|^\s*/\*.*\*/\s*$') {
            continue
        }
        
        # Determine script type and create appropriate filename
        $scriptType = "inline"
        $isModule = $false
        
        if ($attributes -match 'type\s*=\s*["'']module["'']') {
            $scriptType = "module"
            $isModule = $true
        } elseif ($attributes -match 'type\s*=\s*["'']importmap["'']') {
            $scriptType = "importmap"
        }
        
        # Generate filename based on content analysis
        $scriptName = Get-ScriptName -Content $scriptContent -FileName $FileName -Counter $scriptCounter -Type $scriptType
        
        $extractedScript = @{
            OriginalTag = $match.Value
            Content = $scriptContent
            Attributes = $attributes
            FileName = $scriptName
            IsModule = $isModule
            Type = $scriptType
        }
        
        $extractedScripts += $extractedScript
        $scriptCounter++
    }
    
    return $extractedScripts
}

# Function to generate meaningful script names based on content
function Get-ScriptName {
    param(
        [string]$Content,
        [string]$FileName,
        [int]$Counter,
        [string]$Type
    )
    
    $baseName = [System.IO.Path]::GetFileNameWithoutExtension($FileName)
    
    # Analyze content to determine functionality
    $functionality = "generic"
    
    if ($Content -match 'firebase|firestore|auth') {
        $functionality = "firebase"
    } elseif ($Content -match 'timer|pomodoro|countdown') {
        $functionality = "timer"
    } elseif ($Content -match 'modal|dialog|popup') {
        $functionality = "modal"
    } elseif ($Content -match 'chart|graph|visualization') {
        $functionality = "visualization"
    } elseif ($Content -match 'energy|fatigue|hologram') {
        $functionality = "energy"
    } elseif ($Content -match 'task|todo|priority') {
        $functionality = "tasks"
    } elseif ($Content -match 'import.*from|export') {
        $functionality = "imports"
    } elseif ($Content -match 'addEventListener|DOMContentLoaded') {
        $functionality = "events"
    } elseif ($Content -match 'window\.|document\.') {
        $functionality = "dom"
    }
    
    $suffix = if ($Type -eq "module") { ".module" } elseif ($Type -eq "importmap") { ".importmap" } else { "" }
    
    return "$baseName-$functionality-$Counter$suffix.js"
}

# Function to make script backend-compatible
function Make-BackendCompatible {
    param([string]$ScriptContent)
    
    # Add Node.js compatibility checks and polyfills
    $backendCompatibleContent = @"
// Backend compatibility layer
if (typeof window === 'undefined') {
    // Node.js environment - add polyfills if needed
    global.window = global.window || {};
    global.document = global.document || {
        addEventListener: function() {},
        getElementById: function() { return null; },
        createElement: function() { return {}; },
        querySelector: function() { return null; },
        querySelectorAll: function() { return []; }
    };
}

// Original script content
$ScriptContent
"@
    
    return $backendCompatibleContent
}

# Function to save extracted script to file
function Save-ExtractedScript {
    param(
        [hashtable]$Script,
        [string]$OutputDir
    )
    
    $scriptPath = Join-Path $OutputDir $Script.FileName
    $content = $Script.Content
    
    # Make script backend-compatible if it contains DOM operations
    if ($content -match 'document\.|window\.|addEventListener|getElementById') {
        $content = Make-BackendCompatible -ScriptContent $content
    }
    
    # Add header comment with metadata
    $header = @"
/**
 * Extracted from HTML file
 * Type: $($Script.Type)
 * Original attributes: $($Script.Attributes)
 * Generated: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
 */

"@
    
    $finalContent = $header + $content
    
    if (-not $DryRun) {
        Set-Content -Path $scriptPath -Value $finalContent -Encoding UTF8
        Write-VerboseOutput "Saved script to: $scriptPath"
    } else {
        Write-Host "Would save script to: $scriptPath" -ForegroundColor Cyan
    }
    
    return $scriptPath
}

# Function to replace inline scripts with external references
function Replace-InlineScripts {
    param(
        [string]$HtmlContent,
        [array]$ExtractedScripts
    )
    
    $modifiedContent = $HtmlContent
    
    foreach ($script in $ExtractedScripts) {
        $replacement = if ($script.IsModule) {
            "<script type=`"module`" src=`"js/extracted/$($script.FileName)`"></script>"
        } else {
            "<script src=`"js/extracted/$($script.FileName)`"></script>"
        }
        
        $modifiedContent = $modifiedContent -replace [regex]::Escape($script.OriginalTag), $replacement
    }
    
    return $modifiedContent
}

# Function to process a single HTML file
function Process-HtmlFile {
    param([string]$FilePath)

    if (-not (Test-Path $FilePath)) {
        Write-Warning "File not found: $FilePath"
        return
    }

    $fileName = Split-Path $FilePath -Leaf
    Write-Host "Processing: $fileName" -ForegroundColor Green

    # Create backup
    $backupPath = Backup-HtmlFile -FilePath $FilePath

    # Read HTML content
    $htmlContent = Get-Content $FilePath -Raw -Encoding UTF8

    # Extract inline scripts
    $extractedScripts = Extract-InlineScripts -HtmlContent $htmlContent -FileName $fileName

    if ($extractedScripts.Count -eq 0) {
        Write-Host "  No inline scripts found in $fileName" -ForegroundColor Gray
        return
    }

    Write-Host "  Found $($extractedScripts.Count) inline script(s)" -ForegroundColor Cyan

    # Save extracted scripts
    $savedScripts = @()
    foreach ($script in $extractedScripts) {
        $scriptPath = Save-ExtractedScript -Script $script -OutputDir $ExtractedScriptsDir
        $savedScripts += $scriptPath
        Write-VerboseOutput "  - Extracted: $($script.FileName) ($($script.Type))"
    }

    # Replace inline scripts with external references
    $modifiedHtml = Replace-InlineScripts -HtmlContent $htmlContent -ExtractedScripts $extractedScripts

    # Save modified HTML file
    if (-not $DryRun) {
        Set-Content -Path $FilePath -Value $modifiedHtml -Encoding UTF8
        Write-Host "  Updated HTML file with external script references" -ForegroundColor Green
    } else {
        Write-Host "  Would update HTML file with external script references" -ForegroundColor Yellow
    }

    return @{
        FileName = $fileName
        ExtractedScripts = $extractedScripts
        SavedScripts = $savedScripts
        BackupPath = $backupPath
    }
}

# Function to generate summary report
function Generate-SummaryReport {
    param([array]$ProcessedFiles)

    Write-Host "`n" + "="*60 -ForegroundColor Magenta
    Write-Host "SCRIPT EXTRACTION SUMMARY REPORT" -ForegroundColor Magenta
    Write-Host "="*60 -ForegroundColor Magenta

    $totalFiles = $ProcessedFiles.Count
    $totalScripts = ($ProcessedFiles | ForEach-Object { $_.ExtractedScripts.Count } | Measure-Object -Sum).Sum

    Write-Host "Total HTML files processed: $totalFiles" -ForegroundColor White
    Write-Host "Total inline scripts extracted: $totalScripts" -ForegroundColor White
    Write-Host "Scripts saved to: $ExtractedScriptsDir" -ForegroundColor White
    Write-Host "Backups saved to: $BackupDir" -ForegroundColor White

    Write-Host "`nDetailed breakdown:" -ForegroundColor Yellow
    foreach ($file in $ProcessedFiles) {
        if ($file.ExtractedScripts.Count -gt 0) {
            Write-Host "  $($file.FileName): $($file.ExtractedScripts.Count) script(s)" -ForegroundColor Cyan
            foreach ($script in $file.ExtractedScripts) {
                Write-Host "    - $($script.FileName) ($($script.Type))" -ForegroundColor Gray
            }
        }
    }

    Write-Host "`nNext steps:" -ForegroundColor Yellow
    Write-Host "1. Review extracted scripts in: $ExtractedScriptsDir" -ForegroundColor White
    Write-Host "2. Test HTML files to ensure functionality is preserved" -ForegroundColor White
    Write-Host "3. Optimize extracted scripts for backend compatibility" -ForegroundColor White
    Write-Host "4. Consider consolidating similar scripts" -ForegroundColor White
}

# Function to create index file for extracted scripts
function Create-ScriptIndex {
    param([array]$ProcessedFiles)

    $indexPath = Join-Path $ExtractedScriptsDir "index.js"
    $indexContent = @"
/**
 * Index file for all extracted scripts
 * This file can be used to import all extracted scripts in Node.js environment
 * Generated: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
 */

// Import all extracted scripts for backend use
"@

    $allScripts = $ProcessedFiles | ForEach-Object { $_.ExtractedScripts } | Where-Object { $_.Type -ne "importmap" }

    foreach ($script in $allScripts) {
        if ($script.IsModule) {
            $indexContent += "`nimport './$($script.FileName)';"
        } else {
            $indexContent += "`nrequire('./$($script.FileName)');"
        }
    }

    $indexContent += @"


// Export functionality for backend use
module.exports = {
    // Add exported functions here as needed
};
"@

    if (-not $DryRun) {
        Set-Content -Path $indexPath -Value $indexContent -Encoding UTF8
        Write-VerboseOutput "Created script index at: $indexPath"
    }
}

# Main execution
Write-Host "Starting script extraction and modularization..." -ForegroundColor Green
Write-Host "Dry run mode: $DryRun" -ForegroundColor $(if ($DryRun) { "Yellow" } else { "Green" })

$processedFiles = @()

foreach ($htmlFile in $HtmlFiles) {
    $filePath = Join-Path $ScriptRoot $htmlFile
    $result = Process-HtmlFile -FilePath $filePath
    if ($result) {
        $processedFiles += $result
    }
}

# Create script index for backend use
Create-ScriptIndex -ProcessedFiles $processedFiles

# Generate summary report
Generate-SummaryReport -ProcessedFiles $processedFiles

Write-Host "`nScript extraction completed!" -ForegroundColor Green

if ($DryRun) {
    Write-Host "This was a dry run. No files were actually modified." -ForegroundColor Yellow
    Write-Host "Run without -DryRun parameter to perform actual extraction." -ForegroundColor Yellow
}
