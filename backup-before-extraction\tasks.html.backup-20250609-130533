﻿<!DOCTYPE html>
<html lang="en">
<head>
    <!-- ============================================
         META TAGS & BASIC CONFIGURATION
         ============================================ -->
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPAce - Academic Task Manager</title>

    <!-- ============================================
         FAVICON & ICONS
         ============================================ -->
    <link rel="icon" type="image/png" href="assets/images/gpace-logo-white.png">
    <link rel="shortcut icon" type="image/png" href="assets/images/gpace-logo-white.png">
    <!-- ============================================
         BOOTSTRAP & UI FRAMEWORKS
         ============================================ -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <!-- ============================================
         COMPONENT STYLES
         ============================================ -->
    <link href="css/sideDrawer.css" rel="stylesheet">
</head>
<body>
<!-- Navigation Bar -->
    <nav class="top-nav">
        <div class="nav-brand d-flex align-items-center">
            <img
                src="assets/images/gpace-logo-white.png"
                alt="GPAce Logo"
                style="height: 60px; margin-right: 0px;"
            >
            <a href="grind.html" style="text-decoration: none; color: inherit;">GPAce</a>
        </div>
        <div class="nav-links">
            <a href="grind.html">Grind Mode</a>
            <a href="study-spaces.html">Grind Station</a>
            <a href="daily-calendar.html">Daily Drip</a>
            <a href="academic-details.html">Brain Juice</a>
            <a href="extracted.html">Hustle Hub</a>
            <a href="subject-marks.html">Subject Marks</a>
            <a href="flashcards.html">Flashcards</a>
        </div>
    </nav>

    <!-- Sound Controls -->
    <div class="sound-controls">
        <button id="toggleSound" class="sound-toggle" aria-label="Toggle Sound">
            <span class="sound-icon">🔊</span>
        </button>
    </div>

    <!-- Main Content Container -->
    <div class="container">
        <header class="main-header fade-in">
            <div class="logo-section">
                <h1>GPAce</h1>
                <p class="tagline">Academic Task Manager</p>
            </div>
            <div class="account-section">
                <div id="userInfo"></div>
                <div id="accountInfo"></div>
                <div class="todoist-integration">
                    <div class="todoist-login-section">
                        <button id="todoistLogin" class="login-btn">Connect with Todoist</button>
                        <button id="todoistLogout" class="logout-btn" style="display: none;">Logout</button>
                    </div>
                    <div class="todoist-status">
                        <!-- Todoist status will be displayed here -->
                    </div>
                </div>
            </div>
        </header>

        <section class="container tasks-dashboard">
            <div class="tasks-filters">
                <select id="projectFilter">
                    <option value="">All Projects</option>
                </select>
                <select id="sectionFilter">
                    <option value="">All Sections</option>
                </select>
                <select id="sortFilter">
                    <option value="dateAddedDesc">Recently Added</option>
                    <option value="dateAddedAsc">Oldest First</option>
                    <option value="priorityDesc">Highest Priority</option>
                    <option value="priorityAsc">Lowest Priority</option>
                    <option value="dueDateAsc">Earliest Due Date</option>
                    <option value="dueDateDesc">Latest Due Date</option>
                </select>
            </div>
            <div id="tasksList" class="tasks-list">
                <!-- Tasks will be dynamically populated here -->
            </div>
        </section>
    </div>

    <!-- Scripts -->

    <!-- ============================================
         JAVASCRIPT IMPORTS
         ============================================ -->    <!-- EXTERNAL LIBRARIES -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- LOCAL SCRIPTS -->
    <script src="js/todoistIntegration.js"></script>
    <script src="js/sideDrawer.js"></script>
    <script src="js/tasksManager.js"></script>
    <script src="js/taskFilters.js"></script>
    <script type="module" src="js/cross-tab-sync.js"></script>
    <!-- INLINE SCRIPTS -->
    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getAuth, signInWithPopup, GoogleAuthProvider } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';

        // Initialize Firebase with config
        const app = initializeApp({
            apiKey: "AIzaSyCdxGGpfoWD_M_6BwWFqWZ-6MAOKTUjIrI",
            authDomain: "mzm-gpace.firebaseapp.com",
            projectId: "mzm-gpace",
            storageBucket: "mzm-gpace.firebasestorage.app",
            messagingSenderId: "************",
            appId: "1:************:web:3aa05a6e133e2066c45187"
        });

        // Set up auth
        const auth = getAuth(app);
        const provider = new GoogleAuthProvider();
        provider.setCustomParameters({ prompt: 'select_account' });

        // Make available globally
        window.auth = auth;
        window.signInWithGoogle = () => signInWithPopup(auth, provider);
    </script>
    <script>
        // Sound toggle functionality
        const toggleSoundBtn = document.getElementById('toggleSound');
        const soundIcon = document.querySelector('.sound-icon');
        let isSoundEnabled = true;

        toggleSoundBtn.addEventListener('click', () => {
            isSoundEnabled = !isSoundEnabled;
            soundIcon.textContent = isSoundEnabled ? '🔊' : '🔈';
            // Additional sound toggle logic here
        });

        // Task management functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize task filters
            const filterBtns = document.querySelectorAll('.filter-btn');
            filterBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    filterBtns.forEach(b => b.classList.remove('active'));
                    this.classList.add('active');
                    // Additional filter logic here
                });
            });

            // Initialize task search
            const searchInput = document.getElementById('taskSearch');
            searchInput.addEventListener('input', function() {
                // Search functionality here
            });
        });
    </script>
    <script src="/js/inject-header.js"></script>
</body>
</html>