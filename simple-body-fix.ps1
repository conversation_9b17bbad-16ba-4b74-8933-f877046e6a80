# Simple script to fix multiple body tags
param([string]$FilePath = "grind.html")

Write-Host "Fixing multiple body tags in: $FilePath" -ForegroundColor Cyan

if (-not (Test-Path $FilePath)) {
    Write-Host "File not found: $FilePath" -ForegroundColor Red
    exit 1
}

# Create backup
$backupPath = "$FilePath.body-fix-backup"
Copy-Item $FilePath $backupPath -Force
Write-Host "Backup created: $backupPath" -ForegroundColor Green

# Read file
$content = Get-Content $FilePath -Raw -Encoding UTF8

# Count current body tags
$beforeCount = ($content | Select-String -Pattern '</body>' -AllMatches).Matches.Count
Write-Host "Before: Found $beforeCount closing body tags" -ForegroundColor Yellow

if ($beforeCount -le 1) {
    Write-Host "No fix needed - file already has correct structure" -ForegroundColor Green
    exit 0
}

# Remove duplicate body tags by replacing all but keeping the last one
# First, let's find all positions
$lines = $content -split "`n"
$bodyTagLineNumbers = @()

for ($i = 0; $i -lt $lines.Count; $i++) {
    if ($lines[$i] -match '</body>') {
        $bodyTagLineNumbers += $i
        Write-Host "Found closing body tag at line $($i + 1): $($lines[$i].Trim())" -ForegroundColor Yellow
    }
}

# Remove all but the last body tag line
for ($i = 0; $i -lt ($bodyTagLineNumbers.Count - 1); $i++) {
    $lineIndex = $bodyTagLineNumbers[$i]
    Write-Host "Removing duplicate body tag from line $($lineIndex + 1)" -ForegroundColor Red
    $lines[$lineIndex] = ""  # Clear the line
}

# Rebuild content
$fixedContent = ($lines -join "`n")

# Clean up excessive empty lines
$fixedContent = $fixedContent -replace "`n`n`n+", "`n`n"

# Write back to file
$fixedContent | Out-File -FilePath $FilePath -Encoding UTF8 -NoNewline

# Verify fix
$afterContent = Get-Content $FilePath -Raw -Encoding UTF8
$afterCount = ($afterContent | Select-String -Pattern '</body>' -AllMatches).Matches.Count
Write-Host ""
Write-Host "After: Found $afterCount closing body tags" -ForegroundColor Green

if ($afterCount -eq 1) {
    Write-Host "SUCCESS: HTML structure is now valid!" -ForegroundColor Green
    Write-Host "Removed $($beforeCount - 1) duplicate closing body tags" -ForegroundColor Green
} else {
    Write-Host "WARNING: Still have $afterCount body tags - manual review needed" -ForegroundColor Yellow
}

Write-Host "Original file backed up as: $backupPath" -ForegroundColor Cyan
Write-Host "Fix completed!" -ForegroundColor Green
