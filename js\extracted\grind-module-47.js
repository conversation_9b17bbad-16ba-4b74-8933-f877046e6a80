﻿/**
 * Extracted from: grind.html
 * Generated: 2025-06-09 13:05:32
 * Type: ES6 Module
 */

// Backend compatibility layer
if (typeof window === 'undefined') {
    global.window = global.window || {};
    global.document = global.document || {
        addEventListener: function() {},
        getElementById: function() { return { style: {}, innerHTML: '', textContent: '' }; },
        createElement: function() { return { style: {}, setAttribute: function() {}, appendChild: function() {} }; },
        querySelector: function() { return null; },
        querySelectorAll: function() { return []; },
        body: { appendChild: function() {}, style: {} },
        head: { appendChild: function() {} }
    };
    global.console = console;
}

// Original script content:
import {
        saveTasksToFirestore,
        loadTasksFromFirestore,
        saveCompletedTaskToFirestore
      } from './js/firestore.js';
      // Make functions available globally
      window.saveTasksToFirestore = saveTasksToFirestore;
      window.loadTasksFromFirestore = loadTasksFromFirestore;
      window.saveCompletedTaskToFirestore = saveCompletedTaskToFirestore;
