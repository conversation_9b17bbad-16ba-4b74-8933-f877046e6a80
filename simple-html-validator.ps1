# Simple HTML Validation Script
param(
    [string]$FilePath = "grind.html"
)

function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

function Write-Success { param([string]$Message) Write-ColorOutput $Message "Green" }
function Write-Warning { param([string]$Message) Write-ColorOutput $Message "Yellow" }
function Write-Error { param([string]$Message) Write-ColorOutput $Message "Red" }
function Write-Info { param([string]$Message) Write-ColorOutput $Message "Cyan" }

Write-Info "Validating HTML structure for: $FilePath"

if (-not (Test-Path $FilePath)) {
    Write-Error "File not found: $FilePath"
    exit 1
}

$content = Get-Content $FilePath -Raw -Encoding UTF8
$issues = @()

# Check for multiple closing body tags
$bodyCloseTags = [regex]::Matches($content, '</body>', [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
Write-Info "Found $($bodyCloseTags.Count) closing body tags"

if ($bodyCloseTags.Count -gt 1) {
    Write-Error "CRITICAL: Multiple closing body tags found ($($bodyCloseTags.Count))"
    $issues += "Multiple closing body tags"

    # Show line numbers where they occur
    $lines = $content -split "`n"
    for ($i = 0; $i -lt $lines.Count; $i++) {
        if ($lines[$i] -match '</body>') {
            Write-Warning "  Line $($i + 1): $($lines[$i].Trim())"
        }
    }
} elseif ($bodyCloseTags.Count -eq 0) {
    Write-Error "CRITICAL: No closing body tag found"
    $issues += "Missing closing body tag"
} else {
    Write-Success "✓ Exactly one closing body tag found"
}

# Check for multiple closing html tags
$htmlCloseTags = [regex]::Matches($content, '</html>', [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
Write-Info "Found $($htmlCloseTags.Count) closing html tags"

if ($htmlCloseTags.Count -gt 1) {
    Write-Error "CRITICAL: Multiple closing html tags found ($($htmlCloseTags.Count))"
    $issues += "Multiple closing html tags"
} elseif ($htmlCloseTags.Count -eq 0) {
    Write-Error "CRITICAL: No closing html tag found"
    $issues += "Missing closing html tag"
} else {
    Write-Success "✓ Exactly one closing html tag found"
}

# Check for multiple opening body tags
$bodyOpenTags = [regex]::Matches($content, '<body[^>]*>', [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
Write-Info "Found $($bodyOpenTags.Count) opening body tags"

if ($bodyOpenTags.Count -gt 1) {
    Write-Error "CRITICAL: Multiple opening body tags found ($($bodyOpenTags.Count))"
    $issues += "Multiple opening body tags"
} elseif ($bodyOpenTags.Count -eq 0) {
    Write-Error "CRITICAL: No opening body tag found"
    $issues += "Missing opening body tag"
} else {
    Write-Success "✓ Exactly one opening body tag found"
}

# Check for multiple opening html tags
$htmlOpenTags = [regex]::Matches($content, '<html[^>]*>', [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
Write-Info "Found $($htmlOpenTags.Count) opening html tags"

if ($htmlOpenTags.Count -gt 1) {
    Write-Error "CRITICAL: Multiple opening html tags found ($($htmlOpenTags.Count))"
    $issues += "Multiple opening html tags"
} else {
    Write-Success "✓ Exactly one opening html tag found"
}

# Check for script tag balance
$scriptOpenTags = [regex]::Matches($content, '<script[^>]*>', [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
$scriptCloseTags = [regex]::Matches($content, '</script>', [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
Write-Info "Found $($scriptOpenTags.Count) opening script tags and $($scriptCloseTags.Count) closing script tags"

if ($scriptOpenTags.Count -ne $scriptCloseTags.Count) {
    Write-Error "HIGH: Mismatched script tags - $($scriptOpenTags.Count) opening vs $($scriptCloseTags.Count) closing"
    $issues += "Mismatched script tags"
} else {
    Write-Success "✓ Script tags are balanced"
}

# Summary
Write-Info ""
Write-Info "============================================"
Write-Info "  VALIDATION SUMMARY"
Write-Info "============================================"

if ($issues.Count -eq 0) {
    Write-Success "🎉 HTML structure is valid!"
} else {
    Write-Error "⚠️  Found $($issues.Count) structural issues:"
    foreach ($issue in $issues) {
        Write-Error "  - $issue"
    }
    Write-Info ""
    Write-Info "These issues should be fixed to ensure proper HTML rendering."
}

Write-Info "Validation completed."
