@echo off
REM Complete Script Modularization Process
REM This batch file will:
REM 1. Extract inline scripts from HTML files
REM 2. Create modular JavaScript files
REM 3. Test the extracted scripts in backend environment
REM 4. Generate reports

setlocal enabledelayedexpansion

echo ============================================
echo   COMPLETE SCRIPT MODULARIZATION PROCESS
echo ============================================
echo.

REM Check prerequisites
echo Checking prerequisites...

REM Check PowerShell
powershell -Command "Write-Host 'PowerShell OK'" >nul 2>&1
if errorlevel 1 (
    echo ERROR: PowerShell is not available
    goto :error
)

REM Check Node.js
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js is not available
    echo Please install Node.js from https://nodejs.org/
    goto :error
)

echo Prerequisites check passed!
echo.

REM Show menu
:menu
echo Choose an operation:
echo 1. Preview extraction (dry run)
echo 2. Extract and modularize scripts
echo 3. Test extracted scripts in backend
echo 4. Complete process (extract + test)
echo 5. List extracted scripts
echo 6. Clean up (remove extracted scripts)
echo 7. Restore from backup
echo 8. Exit
echo.

set /p choice="Enter your choice (1-8): "

if "%choice%"=="1" goto :preview
if "%choice%"=="2" goto :extract
if "%choice%"=="3" goto :test
if "%choice%"=="4" goto :complete
if "%choice%"=="5" goto :list
if "%choice%"=="6" goto :cleanup
if "%choice%"=="7" goto :restore
if "%choice%"=="8" goto :exit
echo Invalid choice. Please try again.
goto :menu

:preview
echo.
echo ============================================
echo   PREVIEW MODE (DRY RUN)
echo ============================================
echo.
echo This will show what scripts would be extracted without making changes.
echo.
powershell -ExecutionPolicy Bypass -File "extract-and-modularize-scripts.ps1" -DryRun -Verbose
echo.
pause
goto :menu

:extract
echo.
echo ============================================
echo   EXTRACTING AND MODULARIZING SCRIPTS
echo ============================================
echo.
echo WARNING: This will modify your HTML files!
echo Backups will be created automatically.
echo.
set /p confirm="Are you sure you want to proceed? (y/N): "
if /i not "%confirm%"=="y" (
    echo Operation cancelled.
    goto :menu
)

echo.
echo Starting extraction process...
powershell -ExecutionPolicy Bypass -File "extract-and-modularize-scripts.ps1" -Verbose

if errorlevel 1 (
    echo ERROR: Script extraction failed
    goto :menu
)

echo.
echo Extraction completed successfully!
echo.
pause
goto :menu

:test
echo.
echo ============================================
echo   TESTING EXTRACTED SCRIPTS IN BACKEND
echo ============================================
echo.

if not exist "js\extracted" (
    echo ERROR: No extracted scripts found.
    echo Please run extraction first (option 2).
    goto :menu
)

echo Choose test mode:
echo 1. Test all scripts
echo 2. Test scripts by pattern
echo 3. Test single script
echo.

set /p testChoice="Enter choice (1-3): "

if "%testChoice%"=="1" (
    echo Testing all extracted scripts...
    node backend-script-runner.js all
) else if "%testChoice%"=="2" (
    set /p pattern="Enter pattern to match: "
    echo Testing scripts matching pattern: !pattern!
    node backend-script-runner.js pattern "!pattern!"
) else if "%testChoice%"=="3" (
    echo Available scripts:
    node backend-script-runner.js list
    echo.
    set /p scriptName="Enter script name: "
    echo Testing script: !scriptName!
    node backend-script-runner.js single "!scriptName!"
) else (
    echo Invalid choice.
    goto :menu
)

echo.
echo Testing completed. Check backend-execution.log for details.
echo.
pause
goto :menu

:complete
echo.
echo ============================================
echo   COMPLETE PROCESS (EXTRACT + TEST)
echo ============================================
echo.
echo This will:
echo 1. Extract all inline scripts from HTML files
echo 2. Create modular JavaScript files
echo 3. Test all extracted scripts in backend environment
echo 4. Generate comprehensive reports
echo.
echo WARNING: This will modify your HTML files!
echo.
set /p confirm="Are you sure you want to proceed? (y/N): "
if /i not "%confirm%"=="y" (
    echo Operation cancelled.
    goto :menu
)

echo.
echo Step 1: Extracting scripts...
powershell -ExecutionPolicy Bypass -File "extract-and-modularize-scripts.ps1" -Verbose

if errorlevel 1 (
    echo ERROR: Script extraction failed
    goto :menu
)

echo.
echo Step 2: Testing extracted scripts...
node backend-script-runner.js all

echo.
echo Complete process finished!
echo Check the following files for results:
echo - backend-execution.log (detailed execution log)
echo - backend-execution-report.json (summary report)
echo - js\extracted\ (extracted script files)
echo - backup-before-extraction\ (HTML file backups)
echo.
pause
goto :menu

:list
echo.
echo ============================================
echo   LISTING EXTRACTED SCRIPTS
echo ============================================
echo.

if not exist "js\extracted" (
    echo No extracted scripts found.
    echo Please run extraction first (option 2).
) else (
    echo Available extracted scripts:
    node backend-script-runner.js list
)

echo.
pause
goto :menu

:cleanup
echo.
echo ============================================
echo   CLEANUP EXTRACTED SCRIPTS
echo ============================================
echo.
echo WARNING: This will delete all extracted scripts!
echo This action cannot be undone.
echo.
set /p confirm="Are you sure you want to delete all extracted scripts? (y/N): "
if /i not "%confirm%"=="y" (
    echo Cleanup cancelled.
    goto :menu
)

if exist "js\extracted" (
    echo Removing extracted scripts directory...
    rmdir /s /q "js\extracted"
    echo Extracted scripts removed.
) else (
    echo No extracted scripts directory found.
)

if exist "backend-execution.log" (
    del "backend-execution.log"
    echo Execution log removed.
)

if exist "backend-execution-report.json" (
    del "backend-execution-report.json"
    echo Execution report removed.
)

echo Cleanup completed.
echo.
pause
goto :menu

:restore
echo.
echo ============================================
echo   RESTORE FROM BACKUP
echo ============================================
echo.

if not exist "backup-before-extraction" (
    echo No backup directory found.
    echo Cannot restore files.
    goto :menu
)

echo Available backups:
dir /b "backup-before-extraction\*.backup-*" 2>nul
if errorlevel 1 (
    echo No backup files found.
    goto :menu
)

echo.
echo WARNING: This will overwrite current HTML files with backup versions!
echo.
set /p confirm="Are you sure you want to restore from backup? (y/N): "
if /i not "%confirm%"=="y" (
    echo Restore cancelled.
    goto :menu
)

echo.
echo Restoring files from backup...
for %%f in (backup-before-extraction\*.backup-*) do (
    for /f "tokens=1 delims=." %%a in ("%%~nf") do (
        if exist "%%a" (
            copy "%%f" "%%a" >nul
            echo Restored: %%a
        )
    )
)

echo.
echo Restore completed.
echo.
pause
goto :menu

:error
echo.
echo Process terminated due to errors.
echo Please fix the issues and try again.
echo.
pause
exit /b 1

:exit
echo.
echo Thank you for using the Script Modularization Tool!
echo.
exit /b 0
