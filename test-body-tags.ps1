# Quick test for multiple body tags
param([string]$FilePath = "grind.html")

Write-Host "Checking for multiple body tags in: $FilePath" -ForegroundColor Cyan

if (-not (Test-Path $FilePath)) {
    Write-Host "File not found: $FilePath" -ForegroundColor Red
    exit 1
}

$content = Get-Content $FilePath -Raw -Encoding UTF8

# Count closing body tags
$bodyCloseTags = ($content | Select-String -Pattern '</body>' -AllMatches).Matches.Count
Write-Host "Found $bodyCloseTags closing body tags" -ForegroundColor Yellow

if ($bodyCloseTags -gt 1) {
    Write-Host "PROBLEM: Multiple closing body tags found!" -ForegroundColor Red
    
    # Show line numbers
    $lines = $content -split "`n"
    for ($i = 0; $i -lt $lines.Count; $i++) {
        if ($lines[$i] -match '</body>') {
            Write-Host "  Line $($i + 1): $($lines[$i].Trim())" -ForegroundColor Yellow
        }
    }
} else {
    Write-Host "OK: Exactly one closing body tag found" -ForegroundColor Green
}

# Count opening body tags
$bodyOpenTags = ($content | Select-String -Pattern '<body[^>]*>' -AllMatches).Matches.Count
Write-Host "Found $bodyOpenTags opening body tags" -ForegroundColor Yellow

if ($bodyOpenTags -gt 1) {
    Write-Host "PROBLEM: Multiple opening body tags found!" -ForegroundColor Red
} else {
    Write-Host "OK: Exactly one opening body tag found" -ForegroundColor Green
}

Write-Host "Check completed." -ForegroundColor Cyan
