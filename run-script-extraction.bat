@echo off
REM Batch file to run the PowerShell script extraction
REM This will extract all inline scripts from HTML files and modularize them

echo ============================================
echo   SCRIPT EXTRACTION AND MODULARIZATION
echo ============================================
echo.

REM Check if PowerShell is available
powershell -Command "Write-Host 'PowerShell is available'" >nul 2>&1
if errorlevel 1 (
    echo ERROR: PowerShell is not available or not in PATH
    echo Please ensure PowerShell is installed and accessible
    pause
    exit /b 1
)

echo Choose an option:
echo 1. Dry run (preview what would be extracted)
echo 2. Full extraction (actually extract and modify files)
echo 3. Verbose dry run (detailed preview)
echo 4. Verbose full extraction (detailed extraction)
echo.

set /p choice="Enter your choice (1-4): "

if "%choice%"=="1" (
    echo Running dry run...
    powershell -ExecutionPolicy Bypass -File "extract-and-modularize-scripts.ps1" -DryRun
) else if "%choice%"=="2" (
    echo Running full extraction...
    echo WARNING: This will modify your HTML files!
    set /p confirm="Are you sure? (y/N): "
    if /i "%confirm%"=="y" (
        powershell -ExecutionPolicy Bypass -File "extract-and-modularize-scripts.ps1"
    ) else (
        echo Extraction cancelled.
    )
) else if "%choice%"=="3" (
    echo Running verbose dry run...
    powershell -ExecutionPolicy Bypass -File "extract-and-modularize-scripts.ps1" -DryRun -Verbose
) else if "%choice%"=="4" (
    echo Running verbose full extraction...
    echo WARNING: This will modify your HTML files!
    set /p confirm="Are you sure? (y/N): "
    if /i "%confirm%"=="y" (
        powershell -ExecutionPolicy Bypass -File "extract-and-modularize-scripts.ps1" -Verbose
    ) else (
        echo Extraction cancelled.
    )
) else (
    echo Invalid choice. Please run the script again.
)

echo.
echo Press any key to exit...
pause >nul
