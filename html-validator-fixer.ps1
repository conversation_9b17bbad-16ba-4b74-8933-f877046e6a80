# HTML Validation and Fixing Script
# This script detects and fixes common HTML structure issues

param(
    [string]$FilePath = "",
    [switch]$CheckAll = $false,
    [switch]$Fix = $false,
    [switch]$WhatIf = $false
)

# Color output functions
function Write-ColorOutput {
    param([string]$Message, [string]$Color = "White")
    Write-Host $Message -ForegroundColor $Color
}

function Write-Success { param([string]$Message) Write-ColorOutput $Message "Green" }
function Write-Warning { param([string]$Message) Write-ColorOutput $Message "Yellow" }
function Write-Error { param([string]$Message) Write-ColorOutput $Message "Red" }
function Write-Info { param([string]$Message) Write-ColorOutput $Message "Cyan" }

# HTML files to check
$htmlFiles = @(
    'index.html',
    'grind.html',
    'academic-details.html',
    'workspace.html',
    'extracted.html',
    'daily-calendar.html',
    'study-spaces.html',
    'subject-marks.html',
    'settings.html',
    'tasks.html',
    'landing.html',
    'sleep-saboteurs.html',
    '404.html',
    'priority-list.html',
    'priority-calculator.html',
    'flashcards.html',
    'instant-test-feedback.html'
)

function Test-HTMLStructure {
    param([string]$FilePath)
    
    if (-not (Test-Path $FilePath)) {
        Write-Warning "File not found: $FilePath"
        return $null
    }
    
    $content = Get-Content $FilePath -Raw -Encoding UTF8
    $issues = @()
    
    # Check for multiple closing body tags
    $bodyCloseTags = [regex]::Matches($content, '</body>', [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
    if ($bodyCloseTags.Count -gt 1) {
        $issues += @{
            Type = "Multiple </body> tags"
            Count = $bodyCloseTags.Count
            Severity = "Critical"
            Description = "Found $($bodyCloseTags.Count) closing body tags, should be exactly 1"
        }
    } elseif ($bodyCloseTags.Count -eq 0) {
        $issues += @{
            Type = "Missing </body> tag"
            Count = 0
            Severity = "Critical"
            Description = "No closing body tag found"
        }
    }
    
    # Check for multiple closing html tags
    $htmlCloseTags = [regex]::Matches($content, '</html>', [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
    if ($htmlCloseTags.Count -gt 1) {
        $issues += @{
            Type = "Multiple </html> tags"
            Count = $htmlCloseTags.Count
            Severity = "Critical"
            Description = "Found $($htmlCloseTags.Count) closing html tags, should be exactly 1"
        }
    } elseif ($htmlCloseTags.Count -eq 0) {
        $issues += @{
            Type = "Missing </html> tag"
            Count = 0
            Severity = "Critical"
            Description = "No closing html tag found"
        }
    }
    
    # Check for multiple opening body tags
    $bodyOpenTags = [regex]::Matches($content, '<body[^>]*>', [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
    if ($bodyOpenTags.Count -gt 1) {
        $issues += @{
            Type = "Multiple <body> tags"
            Count = $bodyOpenTags.Count
            Severity = "Critical"
            Description = "Found $($bodyOpenTags.Count) opening body tags, should be exactly 1"
        }
    }
    
    # Check for multiple opening html tags
    $htmlOpenTags = [regex]::Matches($content, '<html[^>]*>', [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
    if ($htmlOpenTags.Count -gt 1) {
        $issues += @{
            Type = "Multiple <html> tags"
            Count = $htmlOpenTags.Count
            Severity = "Critical"
            Description = "Found $($htmlOpenTags.Count) opening html tags, should be exactly 1"
        }
    }
    
    # Check for multiple head sections
    $headOpenTags = [regex]::Matches($content, '<head[^>]*>', [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
    $headCloseTags = [regex]::Matches($content, '</head>', [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
    if ($headOpenTags.Count -gt 1 -or $headCloseTags.Count -gt 1) {
        $issues += @{
            Type = "Multiple <head> sections"
            Count = [Math]::Max($headOpenTags.Count, $headCloseTags.Count)
            Severity = "Critical"
            Description = "Found multiple head sections, should be exactly 1"
        }
    }
    
    # Check for DOCTYPE declaration
    if ($content -notmatch '<!DOCTYPE\s+html>') {
        $issues += @{
            Type = "Missing DOCTYPE"
            Count = 0
            Severity = "Warning"
            Description = "No HTML5 DOCTYPE declaration found"
        }
    }
    
    # Check for unclosed script tags
    $scriptOpenTags = [regex]::Matches($content, '<script[^>]*>', [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
    $scriptCloseTags = [regex]::Matches($content, '</script>', [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
    if ($scriptOpenTags.Count -ne $scriptCloseTags.Count) {
        $issues += @{
            Type = "Mismatched script tags"
            Count = [Math]::Abs($scriptOpenTags.Count - $scriptCloseTags.Count)
            Severity = "High"
            Description = "Found $($scriptOpenTags.Count) opening and $($scriptCloseTags.Count) closing script tags"
        }
    }
    
    # Check for empty title tags
    if ($content -match '<title[^>]*></title>') {
        $issues += @{
            Type = "Empty title tag"
            Count = 1
            Severity = "Warning"
            Description = "Title tag is empty"
        }
    }
    
    return @{
        FilePath = $FilePath
        Issues = $issues
        IsValid = ($issues | Where-Object { $_.Severity -eq "Critical" }).Count -eq 0
    }
}

function Fix-HTMLStructure {
    param([string]$FilePath)
    
    if (-not (Test-Path $FilePath)) {
        Write-Warning "File not found: $FilePath"
        return $false
    }
    
    Write-Info "Fixing HTML structure issues in: $FilePath"
    
    # Create backup
    $backupPath = "$FilePath.validation-backup"
    Copy-Item $FilePath $backupPath -Force
    Write-Info "  Backup created: $backupPath"
    
    $content = Get-Content $FilePath -Raw -Encoding UTF8
    $originalContent = $content
    $fixesApplied = @()
    
    # Fix multiple closing body tags - keep only the last one
    $bodyCloseTags = [regex]::Matches($content, '</body>', [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
    if ($bodyCloseTags.Count -gt 1) {
        # Remove all but the last closing body tag
        for ($i = 0; $i -lt ($bodyCloseTags.Count - 1); $i++) {
            $content = $content.Remove($bodyCloseTags[$i].Index - ($i * 7), 7)
        }
        $fixesApplied += "Removed $($bodyCloseTags.Count - 1) duplicate </body> tags"
    }
    
    # Fix multiple closing html tags - keep only the last one
    $htmlCloseTags = [regex]::Matches($content, '</html>', [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
    if ($htmlCloseTags.Count -gt 1) {
        # Remove all but the last closing html tag
        for ($i = 0; $i -lt ($htmlCloseTags.Count - 1); $i++) {
            $content = $content.Remove($htmlCloseTags[$i].Index - ($i * 7), 7)
        }
        $fixesApplied += "Removed $($htmlCloseTags.Count - 1) duplicate </html> tags"
    }
    
    # Fix multiple opening body tags - keep only the first one
    $bodyOpenTags = [regex]::Matches($content, '<body[^>]*>', [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
    if ($bodyOpenTags.Count -gt 1) {
        # Remove all but the first opening body tag
        for ($i = $bodyOpenTags.Count - 1; $i -gt 0; $i--) {
            $tagLength = $bodyOpenTags[$i].Length
            $content = $content.Remove($bodyOpenTags[$i].Index, $tagLength)
        }
        $fixesApplied += "Removed $($bodyOpenTags.Count - 1) duplicate <body> tags"
    }
    
    # Add DOCTYPE if missing
    if ($content -notmatch '<!DOCTYPE\s+html>') {
        if ($content -match '<html[^>]*>') {
            $content = $content -replace '<html[^>]*>', "<!DOCTYPE html>`n$&"
            $fixesApplied += "Added HTML5 DOCTYPE declaration"
        }
    }
    
    # Clean up excessive whitespace
    $content = [regex]::Replace($content, '\n\s*\n\s*\n', "`n`n", [System.Text.RegularExpressions.RegexOptions]::Multiline)
    
    if ($content -ne $originalContent) {
        $content | Out-File -FilePath $FilePath -Encoding UTF8 -NoNewline
        Write-Success "  ✓ Fixed HTML structure issues"
        foreach ($fix in $fixesApplied) {
            Write-Success "    - $fix"
        }
        return $true
    } else {
        Write-Info "  No fixes needed"
        return $false
    }
}

# Main execution
Write-ColorOutput "============================================" "Magenta"
Write-ColorOutput "  HTML VALIDATION AND FIXING SCRIPT" "Magenta"
Write-ColorOutput "============================================" "Magenta"
Write-Info ""

if ($WhatIf) {
    Write-Warning "WHAT-IF MODE: No files will be modified"
    Write-Info ""
}

$filesToCheck = @()
if ($CheckAll) {
    $filesToCheck = $htmlFiles
} elseif ($FilePath -ne "") {
    $filesToCheck = @($FilePath)
} else {
    Write-Error "Please specify a file with -FilePath or use -CheckAll to check all HTML files"
    Write-Info "Usage examples:"
    Write-Info "  .\html-validator-fixer.ps1 -FilePath 'grind.html'"
    Write-Info "  .\html-validator-fixer.ps1 -CheckAll"
    Write-Info "  .\html-validator-fixer.ps1 -CheckAll -Fix"
    Write-Info "  .\html-validator-fixer.ps1 -FilePath 'grind.html' -Fix -WhatIf"
    exit 1
}

$totalFiles = 0
$filesWithIssues = 0
$filesFixed = 0
$allIssues = @()

foreach ($file in $filesToCheck) {
    if (-not (Test-Path $file)) {
        Write-Warning "File not found: $file"
        continue
    }

    $totalFiles++
    Write-Info "Checking: $file"

    $result = Test-HTMLStructure $file

    if ($result.Issues.Count -gt 0) {
        $filesWithIssues++
        Write-Warning "  Found $($result.Issues.Count) issue(s):"

        foreach ($issue in $result.Issues) {
            $color = switch ($issue.Severity) {
                "Critical" { "Red" }
                "High" { "Yellow" }
                "Warning" { "Cyan" }
                default { "White" }
            }
            Write-ColorOutput "    [$($issue.Severity)] $($issue.Type): $($issue.Description)" $color
            $allIssues += $issue
        }

        if ($Fix -and -not $WhatIf) {
            if (Fix-HTMLStructure $file) {
                $filesFixed++
            }
        } elseif ($Fix -and $WhatIf) {
            Write-Info "  [WHAT-IF] Would attempt to fix issues in $file"
        }
    } else {
        Write-Success "  ✓ No issues found"
    }

    Write-Info ""
}

# Summary
Write-ColorOutput "============================================" "Magenta"
Write-ColorOutput "  VALIDATION SUMMARY" "Magenta"
Write-ColorOutput "============================================" "Magenta"

Write-Info "Files checked: $totalFiles"
Write-Info "Files with issues: $filesWithIssues"

if ($Fix -and -not $WhatIf) {
    Write-Info "Files fixed: $filesFixed"
}

# Issue breakdown
$criticalIssues = ($allIssues | Where-Object { $_.Severity -eq "Critical" }).Count
$highIssues = ($allIssues | Where-Object { $_.Severity -eq "High" }).Count
$warningIssues = ($allIssues | Where-Object { $_.Severity -eq "Warning" }).Count

if ($allIssues.Count -gt 0) {
    Write-Info ""
    Write-Info "Issue breakdown:"
    if ($criticalIssues -gt 0) { Write-Error "  Critical issues: $criticalIssues" }
    if ($highIssues -gt 0) { Write-Warning "  High priority issues: $highIssues" }
    if ($warningIssues -gt 0) { Write-Info "  Warnings: $warningIssues" }

    if ($criticalIssues -gt 0) {
        Write-Info ""
        Write-Error "⚠️  CRITICAL ISSUES FOUND! These can break HTML rendering."
        if (-not $Fix) {
            Write-Info "Run with -Fix to automatically resolve these issues."
        }
    }
} else {
    Write-Success "🎉 All HTML files are structurally valid!"
}

Write-Info ""
Write-Info "Script completed at $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
