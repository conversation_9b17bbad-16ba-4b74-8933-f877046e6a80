﻿/**
 * Extracted from: extracted.html
 * Generated: 2025-06-09 13:05:32
 * Type: ES6 Module
 */

// Backend compatibility layer
if (typeof window === 'undefined') {
    global.window = global.window || {};
    global.document = global.document || {
        addEventListener: function() {},
        getElementById: function() { return { style: {}, innerHTML: '', textContent: '' }; },
        createElement: function() { return { style: {}, setAttribute: function() {}, appendChild: function() {} }; },
        querySelector: function() { return null; },
        querySelectorAll: function() { return []; },
        body: { appendChild: function() {}, style: {} },
        head: { appendChild: function() {} }
    };
    global.console = console;
}

// Original script content:
// State manager for sharing data between pages
        window.appState = {
            initialized: false,
            cache: {},
            projectCache: {
                tasks: {},
                get weightages() {
                    return window.appState?.cache?.weightages || null;
                },
                lastFetch: {},
                isFetching: {},
                cacheTTL: 30000,

                // Check if cache is still valid
                isValid: function(projectId) {
                    const now = Date.now();
                    return this.tasks[projectId] &&
                           this.lastFetch[projectId] &&
                           (now - this.lastFetch[projectId] < this.cacheTTL);
                },

                // Store project tasks in cache
                setTasks: function(projectId, tasks) {
                    this.tasks[projectId] = tasks;
                    this.lastFetch[projectId] = Date.now();
                    this.isFetching[projectId] = false;

                    // Also store in app state for cross-page persistence
                    window.appState.cache[`tasks-${projectId}`] = tasks;
                },

                // Clear cache for a specific project
                clearProject: function(projectId) {
                    delete this.tasks[projectId];
                    delete this.lastFetch[projectId];
                    delete window.appState.cache[`tasks-${projectId}`];
                },

                // Force refresh data for a project
                refreshProject: async function(projectId) {
                    if (this.isFetching[projectId]) return;

                    this.isFetching[projectId] = true;

                    try {
                        // Try to get from app state first
                        if (window.appState.cache[`tasks-${projectId}`]) {
                            this.setTasks(projectId, window.appState.cache[`tasks-${projectId}`]);
                            return this.tasks[projectId];
                        }

                        const tasks = await loadTasksFromFirestore(projectId) || [];
                        this.setTasks(projectId, tasks);
                        return tasks;
                    } catch (error) {
                        console.error('Error refreshing project data:', error);
                        this.isFetching[projectId] = false;
                        throw error;
                    }
                }
            },

            async initialize() {
                if (this.initialized) return;

                // Initialize Firebase only once
                const app = initializeApp({
                    apiKey: "AIzaSyCdxGGpfoWD_M_6BwWFqWZ-6MAOKTUjIrI",
                    authDomain: "mzm-gpace.firebaseapp.com",
                    projectId: "mzm-gpace",
                    storageBucket: "mzm-gpace.firebasestorage.app",
                    messagingSenderId: "949014366726",
                    appId: "1:949014366726:web:3aa05a6e133e2066c45187"
                });

                const auth = getAuth(app);
                const provider = new GoogleAuthProvider();

                // Store in global state
                this.auth = auth;
                this.provider = provider;
                this.db = getFirestore(app);

                // Initialize APIs
                await Promise.all([
                    googleDriveAPI.initialize(),
                    this.preloadCommonData()
                ]);

                this.initialized = true;
                console.log('App state initialized');
            },

            async preloadCommonData() {
                if (this.auth?.currentUser) {
                    // Preload subjects
                    const subjects = JSON.parse(localStorage.getItem('academicSubjects') || '[]');
                    this.cache.subjects = subjects;

                    // Preload weightages
                    try {
                        this.cache.weightages = await loadWeightagesFromFirestore();
                    } catch (error) {
                        console.warn('Could not preload weightages:', error);
                    }
                }
            }
        };

        // Initialize app state when the module loads
        window.appState.initialize().catch(console.error);

        // Add page transition manager
        window.pageManager = {
            currentPage: window.location.pathname,
            pageCache: new Map(),

            preloadPage(url) {
                if (this.pageCache.has(url)) return;

                const link = document.createElement('link');
                link.rel = 'prefetch';
                link.href = url;
                document.head.appendChild(link);
                this.pageCache.set(url, true);
            },

            navigateTo(url) {
                // Save current scroll position
                const scrollPos = {
                    x: window.scrollX,
                    y: window.scrollY
                };
                sessionStorage.setItem('scrollPos-' + this.currentPage, JSON.stringify(scrollPos));

                // Navigate to new page
                window.location.href = url;
            }
        };

        // Preload linked pages
        document.querySelectorAll('a').forEach(link => {
            const url = link.getAttribute('href');
            if (url && !url.startsWith('#') && !url.startsWith('javascript:')) {
                window.pageManager.preloadPage(url);
            }
        });

        // Restore scroll position if coming back to a page
        window.addEventListener('load', () => {
            const scrollPos = sessionStorage.getItem('scrollPos-' + window.location.pathname);
            if (scrollPos) {
                const { x, y } = JSON.parse(scrollPos);
                window.scrollTo(x, y);
            }
        });
