﻿/**
 * Extracted from: grind.html
 * Generated: 2025-06-09 13:05:32
 * Type: ES6 Module
 */

// Backend compatibility layer
if (typeof window === 'undefined') {
    global.window = global.window || {};
    global.document = global.document || {
        addEventListener: function() {},
        getElementById: function() { return { style: {}, innerHTML: '', textContent: '' }; },
        createElement: function() { return { style: {}, setAttribute: function() {}, appendChild: function() {} }; },
        querySelector: function() { return null; },
        querySelectorAll: function() { return []; },
        body: { appendChild: function() {}, style: {} },
        head: { appendChild: function() {} }
    };
    global.console = console;
}

// Original script content:
// Import Firestore functions
      import { saveSnippetsToFirestore, loadSnippetsFromFirestore, setupSnippetsRealtimeSync } from './js/firestore.js';
      import { auth } from './js/firestore.js';
      import flashcardTaskIntegration from './js/flashcardTaskIntegration.js';

      // Make functions available globally
      window.saveSnippetsToFirestore = saveSnippetsToFirestore;
      window.loadSnippetsFromFirestore = loadSnippetsFromFirestore;
      window.setupSnippetsRealtimeSync = setupSnippetsRealtimeSync;
      window.auth = auth;

      // Initialize flashcard task integration
      document.addEventListener('DOMContentLoaded', function() {
        flashcardTaskIntegration.init();
      });
