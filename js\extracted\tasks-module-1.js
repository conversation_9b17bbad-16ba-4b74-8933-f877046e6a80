﻿/**
 * Extracted from: tasks.html
 * Generated: 2025-06-09 13:05:33
 * Type: ES6 Module
 */

// Backend compatibility layer
if (typeof window === 'undefined') {
    global.window = global.window || {};
    global.document = global.document || {
        addEventListener: function() {},
        getElementById: function() { return { style: {}, innerHTML: '', textContent: '' }; },
        createElement: function() { return { style: {}, setAttribute: function() {}, appendChild: function() {} }; },
        querySelector: function() { return null; },
        querySelectorAll: function() { return []; },
        body: { appendChild: function() {}, style: {} },
        head: { appendChild: function() {} }
    };
    global.console = console;
}

// Original script content:
import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getAuth, signInWithPopup, GoogleAuthProvider } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';

        // Initialize Firebase with config
        const app = initializeApp({
            apiKey: "AIzaSyCdxGGpfoWD_M_6BwWFqWZ-6MAOKTUjIrI",
            authDomain: "mzm-gpace.firebaseapp.com",
            projectId: "mzm-gpace",
            storageBucket: "mzm-gpace.firebasestorage.app",
            messagingSenderId: "************",
            appId: "1:************:web:3aa05a6e133e2066c45187"
        });

        // Set up auth
        const auth = getAuth(app);
        const provider = new GoogleAuthProvider();
        provider.setCustomParameters({ prompt: 'select_account' });

        // Make available globally
        window.auth = auth;
        window.signInWithGoogle = () => signInWithPopup(auth, provider);
