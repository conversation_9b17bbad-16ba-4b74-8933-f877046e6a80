﻿/**
 * Extracted from: extracted.html
 * Generated: 2025-06-09 13:05:32
 * Type: ES6 Module
 */

// Backend compatibility layer
if (typeof window === 'undefined') {
    global.window = global.window || {};
    global.document = global.document || {
        addEventListener: function() {},
        getElementById: function() { return { style: {}, innerHTML: '', textContent: '' }; },
        createElement: function() { return { style: {}, setAttribute: function() {}, appendChild: function() {} }; },
        querySelector: function() { return null; },
        querySelectorAll: function() { return []; },
        body: { appendChild: function() {}, style: {} },
        head: { appendChild: function() {} }
    };
    global.console = console;
}

// Original script content:
import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getAuth, signInWithPopup, GoogleAuthProvider } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';
        import { getFirestore } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';
        import { saveTasksToFirestore, loadTasksFromFirestore, saveCompletedTaskToFirestore, saveWeightagesToFirestore, loadWeightagesFromFirestore } from './js/firestore.js';
        import crossTabSync from './js/cross-tab-sync.js';
        import userGuidance from './js/userGuidance.js';
        import googleDriveAPI from './js/googleDriveApi.js';
        import taskAttachments from './js/taskAttachments.js';
        import dataSyncManager from './js/data-sync-manager.js';

        // Initialize Firebase with config
        const app = initializeApp({
            apiKey: "AIzaSyCdxGGpfoWD_M_6BwWFqWZ-6MAOKTUjIrI",
            authDomain: "mzm-gpace.firebaseapp.com",
            projectId: "mzm-gpace",
            storageBucket: "mzm-gpace.firebasestorage.app",
            messagingSenderId: "************",
            appId: "1:************:web:3aa05a6e133e2066c45187"
        });

        // Set up auth
        const auth = getAuth(app);
        const provider = new GoogleAuthProvider();

        // Make auth and functions available globally
        window.auth = auth;
        window.signInWithGoogle = async () => {
            try {
                await signInWithPopup(auth, provider);
            } catch (error) {
                console.error('Error signing in with Google:', error);
            }
        };
        window.signOutUser = async () => {
            try {
                await auth.signOut();
            } catch (error) {
                console.error('Error signing out:', error);
            }
        };
        window.saveTasksToFirestore = saveTasksToFirestore;
        window.loadTasksFromFirestore = loadTasksFromFirestore;
        window.saveCompletedTaskToFirestore = saveCompletedTaskToFirestore;
        window.saveWeightagesToFirestore = saveWeightagesToFirestore;
        window.loadWeightagesFromFirestore = loadWeightagesFromFirestore;

        // Initialize Google Drive API without await
        googleDriveAPI.initialize()
            .then(() => {
                console.log('Google Drive API initialized successfully');
            })
            .catch(error => {
                console.error('Error initializing Google Drive API:', error);
            });

        // Make task attachments globally available
        window.taskAttachments = taskAttachments;

        // Add cross-tab synchronization for page reload
        crossTabSync.onUserAction('task-update', (data) => {
            console.log(' Task update received for project:', data.projectId);
            console.log(' Reloading page due to task update');
            location.reload();
        });
