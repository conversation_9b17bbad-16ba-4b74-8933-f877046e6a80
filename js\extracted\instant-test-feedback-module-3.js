﻿/**
 * Extracted from: instant-test-feedback.html
 * Generated: 2025-06-09 13:05:33
 * Type: ES6 Module
 */

// Backend compatibility layer
if (typeof window === 'undefined') {
    global.window = global.window || {};
    global.document = global.document || {
        addEventListener: function() {},
        getElementById: function() { return { style: {}, innerHTML: '', textContent: '' }; },
        createElement: function() { return { style: {}, setAttribute: function() {}, appendChild: function() {} }; },
        querySelector: function() { return null; },
        querySelectorAll: function() { return []; },
        body: { appendChild: function() {}, style: {} },
        head: { appendChild: function() {} }
    };
    global.console = console;
}

// Original script content:
import { saveSubjectsToFirestore, loadSubjectsFromFirestore } from './js/firestore.js';
        import { initializeFirestoreData } from './js/initFirestoreData.js';

        // Make functions available globally
        window.loadSubjectsFromFirestore = loadSubjectsFromFirestore;
        window.saveSubjectsToFirestore = saveSubjectsToFirestore;
        window.initializeFirestoreData = initializeFirestoreData;

        // Initialize data when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                if (typeof window.initializeFirestoreData === 'function') {
                    window.initializeFirestoreData();
                }
            }, 1500);
        });
