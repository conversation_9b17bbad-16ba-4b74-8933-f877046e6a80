﻿/**
 * Extracted from: grind.html
 * Generated: 2025-06-09 13:05:32
 * Type: Regular Script
 */

// Backend compatibility layer
if (typeof window === 'undefined') {
    global.window = global.window || {};
    global.document = global.document || {
        addEventListener: function() {},
        getElementById: function() { return { style: {}, innerHTML: '', textContent: '' }; },
        createElement: function() { return { style: {}, setAttribute: function() {}, appendChild: function() {} }; },
        querySelector: function() { return null; },
        querySelectorAll: function() { return []; },
        body: { appendChild: function() {}, style: {} },
        head: { appendChild: function() {} }
    };
    global.console = console;
}

// Original script content:
// getCurrentTask function is now defined at the top of the document

    // ... existing code ...
    // Handle subject materials
    async function loadSubjectMaterials(subjectTag) {
      try {
        const materialsList = document.querySelector('.subject-materials-list');
        if (!materialsList) {
          console.warn('Subject materials list element not found');
          return;
        }

        materialsList.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div>';

        // Check if Google Drive API is initialized
        if (!window.googleDriveAPI || typeof window.googleDriveAPI.getSubjectFiles !== 'function') {
          console.error('Google Drive API not initialized or getSubjectFiles method not available');
          materialsList.innerHTML = '<p class="text-danger">Google Drive API not ready. Please refresh the page.</p>';
          return;
        }

        const files = await window.googleDriveAPI.getSubjectFiles(subjectTag);
        if (files.length === 0) {
          materialsList.innerHTML = '<p class="text-muted">No materials uploaded yet</p>';
          return;
        }

        materialsList.innerHTML = '';
        files.forEach(file => {
          const materialType = file.appProperties?.materialType || 'general';
          const div = document.createElement('div');
          div.className = 'subject-material-item';
          div.innerHTML = `
                    <div class="material-info">
                        <i class="bi bi-file-earmark-text"></i>
                        <span>${file.name}</span>
                        <span class="material-type-badge">${materialType}</span>
                    </div>
                    <div class="material-actions">
                        <button onclick="window.googleDriveAPI.showPreview('${file.id}')" title="Preview">
                            <i class="bi bi-eye"></i>
                        </button>
                        <a href="${file.webViewLink}" target="_blank" title="Open">
                            <button><i class="bi bi-box-arrow-up-right"></i></button>
                        </a>
                        <button onclick="deleteSubjectMaterial('${file.id}', '${subjectTag}')" title="Delete" class="text-danger">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                `;
          materialsList.appendChild(div);
        });
      } catch (error) {
        console.error('Error loading subject materials:', error);
        const materialsList = document.querySelector('.subject-materials-list');
        if (materialsList) {
          materialsList.innerHTML = '<p class="text-danger">Error loading materials</p>';
        }
      }
    }
    // Add delete function for subject materials
    async function deleteSubjectMaterial(fileId, subjectTag) {
      if (!confirm('Are you sure you want to delete this material? This action cannot be undone.')) {
        return;
      }
      try {
        await window.googleDriveAPI.deleteFile(fileId);
        // Update local storage
        const subjectMaterialsJson = localStorage.getItem('subjectMaterials') || '{}';
        const subjectMaterials = JSON.parse(subjectMaterialsJson);
        if (subjectMaterials[subjectTag]) {
          subjectMaterials[subjectTag] = subjectMaterials[subjectTag].filter(
            material => material.fileId !== fileId
          );
          localStorage.setItem('subjectMaterials', JSON.stringify(subjectMaterials));
        }
        // Reload the materials list
        await loadSubjectMaterials(subjectTag);
        showToast('Success', 'Material deleted successfully', 'success');
      } catch (error) {
        console.error('Error deleting subject material:', error);
        showToast('Error', 'Failed to delete material', 'error');
      }
    }
    // Handle subject material upload
    document.getElementById('uploadSubjectMaterial').addEventListener('click', async () => {
      const fileInput = document.getElementById('subjectMaterialFile');
      const materialType = document.getElementById('materialType').value;
      const file = fileInput.files[0];
      if (!file) {
        alert('Please select a file to upload');
        return;
      }
      try {
        const currentTask = getCurrentTask();
        if (!currentTask || !currentTask.projectId) {
          throw new Error('No active task or project');
        }

        // Check if Google Drive API is initialized
        if (!window.googleDriveAPI || typeof window.googleDriveAPI.uploadSubjectFile !== 'function') {
          throw new Error('Google Drive API is not initialized. Please refresh the page and try again.');
        }

        const uploadButton = document.getElementById('uploadSubjectMaterial');
        const originalText = uploadButton.innerHTML;
        uploadButton.disabled = true;
        uploadButton.innerHTML = '<span class="spinner-border spinner-border-sm"></span> Uploading...';
        await window.googleDriveAPI.uploadSubjectFile(file, currentTask.projectId, materialType);
        // Close modal and reload materials
        bootstrap.Modal.getInstance(document.getElementById('addSubjectMaterialModal')).hide();
        await loadSubjectMaterials(currentTask.projectId);
        // Show success toast
        showToast('Success', 'Material uploaded successfully', 'success');
      } catch (error) {
        console.error('Error uploading subject material:', error);
        showToast('Error', error.message, 'error');
      } finally {
        uploadButton.disabled = false;
        uploadButton.innerHTML = originalText;
      }
    });
    // Update loadTaskIntoContainer to include subject materials
    function loadTaskIntoContainer(task) {
      // ... existing task loading code ...
      // Load subject materials if task has a project
      if (task.projectId) {
        loadSubjectMaterials(task.projectId);
      }
    }
