# Fix all HTML structure issues across all files
$filesToFix = @(
    'grind.html',
    'extracted.html', 
    'subject-marks.html',
    'settings.html',
    'landing.html',
    'sleep-saboteurs.html',
    '404.html',
    'priority-list.html',
    'priority-calculator.html'
)

Write-Host "============================================" -ForegroundColor Magenta
Write-Host "  HTML STRUCTURE MASS FIXER" -ForegroundColor Magenta
Write-Host "============================================" -ForegroundColor Magenta
Write-Host ""

$totalFixed = 0
$totalIssues = 0

foreach ($file in $filesToFix) {
    if (-not (Test-Path $file)) {
        Write-Host "⚠️  File not found: $file" -ForegroundColor Yellow
        continue
    }
    
    Write-Host "Processing: $file" -ForegroundColor Cyan
    
    # Create backup
    $backupPath = "$file.mass-fix-backup"
    Copy-Item $file $backupPath -Force
    Write-Host "  📁 Backup created: $backupPath" -ForegroundColor Green
    
    # Read file
    $content = Get-Content $file -Raw -Encoding UTF8
    $lines = $content -split "`n"
    $issuesFixed = 0
    
    # Fix multiple closing body tags
    $bodyTagLines = @()
    for ($i = 0; $i -lt $lines.Count; $i++) {
        if ($lines[$i] -match '</body>') {
            $bodyTagLines += $i
        }
    }
    
    if ($bodyTagLines.Count -gt 1) {
        Write-Host "  🔧 Fixing $($bodyTagLines.Count) closing body tags..." -ForegroundColor Yellow
        # Remove all but the last body tag
        for ($i = 0; $i -lt ($bodyTagLines.Count - 1); $i++) {
            $lines[$bodyTagLines[$i]] = ""
            $issuesFixed++
            $totalIssues++
        }
        Write-Host "    ✅ Removed $($bodyTagLines.Count - 1) duplicate closing body tags" -ForegroundColor Green
    }
    
    # Fix multiple closing html tags
    $htmlTagLines = @()
    for ($i = 0; $i -lt $lines.Count; $i++) {
        if ($lines[$i] -match '</html>') {
            $htmlTagLines += $i
        }
    }
    
    if ($htmlTagLines.Count -gt 1) {
        Write-Host "  🔧 Fixing $($htmlTagLines.Count) closing html tags..." -ForegroundColor Yellow
        # Remove all but the last html tag
        for ($i = 0; $i -lt ($htmlTagLines.Count - 1); $i++) {
            $lines[$htmlTagLines[$i]] = ""
            $issuesFixed++
            $totalIssues++
        }
        Write-Host "    ✅ Removed $($htmlTagLines.Count - 1) duplicate closing html tags" -ForegroundColor Green
    }
    
    if ($issuesFixed -gt 0) {
        # Rebuild content and clean up empty lines
        $fixedContent = ($lines -join "`n")
        $fixedContent = $fixedContent -replace "`n`n`n+", "`n`n"
        
        # Write back to file
        $fixedContent | Out-File -FilePath $file -Encoding UTF8 -NoNewline
        
        Write-Host "  ✅ Fixed $issuesFixed issues in $file" -ForegroundColor Green
        $totalFixed++
    } else {
        Write-Host "  ℹ️  No issues found in $file" -ForegroundColor Cyan
    }
    
    Write-Host ""
}

# Summary
Write-Host "============================================" -ForegroundColor Magenta
Write-Host "  MASS FIX SUMMARY" -ForegroundColor Magenta
Write-Host "============================================" -ForegroundColor Magenta
Write-Host "Files processed: $($filesToFix.Count)" -ForegroundColor Cyan
Write-Host "Files fixed: $totalFixed" -ForegroundColor Green
Write-Host "Total issues resolved: $totalIssues" -ForegroundColor Green

if ($totalIssues -gt 0) {
    Write-Host ""
    Write-Host "🎉 Successfully fixed all HTML structure issues!" -ForegroundColor Green
    Write-Host "All original files are backed up with .mass-fix-backup extension" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "Your HTML files now have valid structure:" -ForegroundColor Green
    Write-Host "  ✅ Exactly one closing body tag per file" -ForegroundColor Green
    Write-Host "  ✅ Exactly one closing html tag per file" -ForegroundColor Green
} else {
    Write-Host ""
    Write-Host "ℹ️  No issues were found to fix" -ForegroundColor Cyan
}

Write-Host ""
Write-Host "Mass fix completed at $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Cyan
