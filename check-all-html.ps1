# Check all HTML files for structural issues
$htmlFiles = @(
    'index.html', 'grind.html', 'academic-details.html', 'workspace.html',
    'extracted.html', 'daily-calendar.html', 'study-spaces.html', 'subject-marks.html',
    'settings.html', 'tasks.html', 'landing.html', 'sleep-saboteurs.html',
    '404.html', 'priority-list.html', 'priority-calculator.html', 'flashcards.html',
    'instant-test-feedback.html'
)

Write-Host "============================================" -ForegroundColor Magenta
Write-Host "  HTML STRUCTURE VALIDATION REPORT" -ForegroundColor Magenta
Write-Host "============================================" -ForegroundColor Magenta
Write-Host ""

$totalFiles = 0
$filesWithIssues = 0
$totalIssues = 0

foreach ($file in $htmlFiles) {
    if (-not (Test-Path $file)) {
        continue
    }
    
    $totalFiles++
    Write-Host "Checking: $file" -ForegroundColor Cyan
    
    $content = Get-Content $file -Raw -Encoding UTF8
    $issues = @()
    
    # Check body tags
    $bodyCloseTags = ($content | Select-String -Pattern '</body>' -AllMatches).Matches.Count
    $bodyOpenTags = ($content | Select-String -Pattern '<body[^>]*>' -AllMatches).Matches.Count
    
    if ($bodyCloseTags -gt 1) {
        $issues += "Multiple closing body tags: $bodyCloseTags"
    }
    if ($bodyCloseTags -eq 0) {
        $issues += "Missing closing body tag"
    }
    if ($bodyOpenTags -gt 1) {
        $issues += "Multiple opening body tags: $bodyOpenTags"
    }
    if ($bodyOpenTags -eq 0) {
        $issues += "Missing opening body tag"
    }
    
    # Check html tags
    $htmlCloseTags = ($content | Select-String -Pattern '</html>' -AllMatches).Matches.Count
    $htmlOpenTags = ($content | Select-String -Pattern '<html[^>]*>' -AllMatches).Matches.Count
    
    if ($htmlCloseTags -gt 1) {
        $issues += "Multiple closing html tags: $htmlCloseTags"
    }
    if ($htmlCloseTags -eq 0) {
        $issues += "Missing closing html tag"
    }
    if ($htmlOpenTags -gt 1) {
        $issues += "Multiple opening html tags: $htmlOpenTags"
    }
    
    # Check script balance
    $scriptOpenTags = ($content | Select-String -Pattern '<script[^>]*>' -AllMatches).Matches.Count
    $scriptCloseTags = ($content | Select-String -Pattern '</script>' -AllMatches).Matches.Count
    
    if ($scriptOpenTags -ne $scriptCloseTags) {
        $issues += "Mismatched script tags: $scriptOpenTags open vs $scriptCloseTags close"
    }
    
    # Report results
    if ($issues.Count -gt 0) {
        $filesWithIssues++
        $totalIssues += $issues.Count
        Write-Host "  ❌ Issues found:" -ForegroundColor Red
        foreach ($issue in $issues) {
            Write-Host "    - $issue" -ForegroundColor Yellow
        }
    } else {
        Write-Host "  ✅ Valid structure" -ForegroundColor Green
    }
    
    Write-Host ""
}

# Summary
Write-Host "============================================" -ForegroundColor Magenta
Write-Host "  SUMMARY" -ForegroundColor Magenta
Write-Host "============================================" -ForegroundColor Magenta
Write-Host "Files checked: $totalFiles" -ForegroundColor Cyan
Write-Host "Files with issues: $filesWithIssues" -ForegroundColor $(if ($filesWithIssues -eq 0) { "Green" } else { "Red" })
Write-Host "Total issues: $totalIssues" -ForegroundColor $(if ($totalIssues -eq 0) { "Green" } else { "Red" })

if ($totalIssues -eq 0) {
    Write-Host ""
    Write-Host "🎉 All HTML files have valid structure!" -ForegroundColor Green
} else {
    Write-Host ""
    Write-Host "⚠️  Issues found that should be fixed for valid HTML" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Report completed at $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Cyan
