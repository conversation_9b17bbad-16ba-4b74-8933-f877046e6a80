﻿/**
 * Extracted from: grind.html
 * Generated: 2025-06-09 13:05:31
 * Type: ES6 Module
 */

// Backend compatibility layer
if (typeof window === 'undefined') {
    global.window = global.window || {};
    global.document = global.document || {
        addEventListener: function() {},
        getElementById: function() { return { style: {}, innerHTML: '', textContent: '' }; },
        createElement: function() { return { style: {}, setAttribute: function() {}, appendChild: function() {} }; },
        querySelector: function() { return null; },
        querySelectorAll: function() { return []; },
        body: { appendChild: function() {}, style: {} },
        head: { appendChild: function() {} }
    };
    global.console = console;
}

// Original script content:
import {
      updateHologramEnergy
    } from './js/energyHologram.js';
    // Add event listeners to fatigue levels
    document.querySelectorAll('.fatigue-level').forEach(level => {
      level.addEventListener('click', function() {
        const energyLevel = parseInt(this.dataset.level);
        // Update hologram
        updateHologramEnergy(energyLevel);
        // Store the energy level
        localStorage.setItem('currentEnergyLevel', energyLevel.toString());
        // Remove selected class from all levels
        document.querySelectorAll('.fatigue-level').forEach(l => l.classList.remove('selected'));
        // Add selected class to clicked level
        this.classList.add('selected');
        // Hide modal and start timer
        hideFatigueModal();
        startTimer();
      });
    });
    // Check for stored energy level on page load
    document.addEventListener('DOMContentLoaded', () => {
      const storedLevel = localStorage.getItem('currentEnergyLevel');
      if (storedLevel) {
        const level = parseInt(storedLevel);
        // Update hologram
        updateHologramEnergy(level);
        // Select the corresponding fatigue level
        const fatigueLevel = document.querySelector(`.fatigue-level[data-level="${level}"]`);
        if (fatigueLevel) {
          fatigueLevel.classList.add('selected');
        }
      }
    });
