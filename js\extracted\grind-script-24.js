﻿/**
 * Extracted from: grind.html
 * Generated: 2025-06-09 13:05:32
 * Type: Regular Script
 */

// Backend compatibility layer
if (typeof window === 'undefined') {
    global.window = global.window || {};
    global.document = global.document || {
        addEventListener: function() {},
        getElementById: function() { return { style: {}, innerHTML: '', textContent: '' }; },
        createElement: function() { return { style: {}, setAttribute: function() {}, appendChild: function() {} }; },
        querySelector: function() { return null; },
        querySelectorAll: function() { return []; },
        body: { appendChild: function() {}, style: {} },
        head: { appendChild: function() {} }
    };
    global.console = console;
}

// Original script content:
// Simple string hashing function for comparing task lists
    function hashString(str) {
      let hash = 0;
      if (str.length === 0) return hash;

      for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // Convert to 32bit integer
      }

      return hash;
    }

    // This initialization code ensures consistent task display
    document.addEventListener('DOMContentLoaded', () => {
      console.log('Initializing priority task handling...');
      // Listen for storage changes from other tabs/pages
      window.addEventListener('storage', function(e) {
        if (e.key === 'calculatedPriorityTasks') {
          console.log('Priority tasks updated in another tab, refreshing display...');
          // Force refresh only if the data has actually changed
          const newTasksHash = hashString(e.newValue || '[]');
          if (newTasksHash !== lastTasksHash) {
            displayPriorityTask();
          } else {
            console.log('Storage event with unchanged data, skipping refresh');
          }
        }
      });
      // Load the priority list sorting script dynamically if it's not already loaded
      if (typeof window.PriorityListSorter === 'undefined') {
        console.log('Loading PriorityListSorter script...');
        const script = document.createElement('script');
        script.src = 'js/priority-list-sorting.js';
        script.onload = function() {
          console.log('PriorityListSorter script loaded, applying sort...');
          if (typeof window.PriorityListSorter !== 'undefined') {
            try {
              window.PriorityListSorter.applySavedSort();
            } catch (e) {
              console.error('Error applying saved sort:', e);
              displayPriorityTask();
            }
          } else {
            console.warn('PriorityListSorter not available after loading script');
            displayPriorityTask();
          }
        };
        script.onerror = function() {
          console.error('Failed to load PriorityListSorter script');
          displayPriorityTask();
        };
        document.body.appendChild(script);
      } else {
        // If the sorter is already loaded, apply saved sort
        console.log('PriorityListSorter already loaded, applying sort...');
        try {
          window.PriorityListSorter.applySavedSort();
        } catch (e) {
          console.error('Error applying saved sort:', e);
          displayPriorityTask();
        }
      }
    });
