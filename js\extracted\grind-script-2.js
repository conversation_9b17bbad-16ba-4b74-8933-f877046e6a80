﻿/**
 * Extracted from: grind.html
 * Generated: 2025-06-09 13:05:31
 * Type: Regular Script
 */

// Backend compatibility layer
if (typeof window === 'undefined') {
    global.window = global.window || {};
    global.document = global.document || {
        addEventListener: function() {},
        getElementById: function() { return { style: {}, innerHTML: '', textContent: '' }; },
        createElement: function() { return { style: {}, setAttribute: function() {}, appendChild: function() {} }; },
        querySelector: function() { return null; },
        querySelectorAll: function() { return []; },
        body: { appendChild: function() {}, style: {} },
        head: { appendChild: function() {} }
    };
    global.console = console;
}

// Original script content:
{
            "imports": {
                "three": "https://unpkg.com/three@0.157.0/build/three.module.js",
                "three/addons/": "https://unpkg.com/three@0.157.0/examples/jsm/",
                "@google/generative-ai": "https://esm.run/@google/generative-ai"
            }
        }
