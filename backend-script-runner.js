/**
 * Backend Script Runner
 * This Node.js script can execute the extracted and modularized scripts
 * in a backend environment without requiring a browser
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const EXTRACTED_SCRIPTS_DIR = path.join(__dirname, 'js', 'extracted');
const LOG_FILE = path.join(__dirname, 'backend-execution.log');

// Global polyfills for browser APIs
global.window = global.window || {
    location: { href: 'http://localhost:3000' },
    localStorage: {
        getItem: (key) => null,
        setItem: (key, value) => {},
        removeItem: (key) => {},
        clear: () => {}
    },
    sessionStorage: {
        getItem: (key) => null,
        setItem: (key, value) => {},
        removeItem: (key) => {},
        clear: () => {}
    },
    addEventListener: (event, callback) => {},
    removeEventListener: (event, callback) => {},
    setTimeout: setTimeout,
    setInterval: setInterval,
    clearTimeout: clearTimeout,
    clearInterval: clearInterval
};

global.document = global.document || {
    addEventListener: (event, callback) => {},
    getElementById: (id) => ({ 
        innerHTML: '', 
        textContent: '', 
        style: {},
        addEventListener: () => {},
        click: () => {},
        focus: () => {}
    }),
    createElement: (tag) => ({
        innerHTML: '',
        textContent: '',
        style: {},
        setAttribute: () => {},
        getAttribute: () => null,
        appendChild: () => {},
        removeChild: () => {},
        addEventListener: () => {}
    }),
    querySelector: (selector) => null,
    querySelectorAll: (selector) => [],
    body: {
        appendChild: () => {},
        removeChild: () => {},
        style: {}
    },
    head: {
        appendChild: () => {},
        removeChild: () => {}
    },
    title: 'Backend Runner',
    cookie: ''
};

// Console polyfill
global.console = console;

// Logger utility
class Logger {
    constructor(logFile) {
        this.logFile = logFile;
        this.initLogFile();
    }

    initLogFile() {
        const timestamp = new Date().toISOString();
        const header = `\n=== Backend Script Execution Log - ${timestamp} ===\n`;
        fs.appendFileSync(this.logFile, header);
    }

    log(level, message, scriptName = null) {
        const timestamp = new Date().toISOString();
        const logEntry = `[${timestamp}] ${level.toUpperCase()}: ${scriptName ? `[${scriptName}] ` : ''}${message}\n`;
        
        console.log(logEntry.trim());
        fs.appendFileSync(this.logFile, logEntry);
    }

    info(message, scriptName = null) {
        this.log('info', message, scriptName);
    }

    warn(message, scriptName = null) {
        this.log('warn', message, scriptName);
    }

    error(message, scriptName = null) {
        this.log('error', message, scriptName);
    }
}

// Script executor class
class BackendScriptRunner {
    constructor() {
        this.logger = new Logger(LOG_FILE);
        this.loadedScripts = new Set();
        this.scriptResults = new Map();
    }

    // Get list of all extracted scripts
    getExtractedScripts() {
        if (!fs.existsSync(EXTRACTED_SCRIPTS_DIR)) {
            this.logger.error('Extracted scripts directory not found. Run script extraction first.');
            return [];
        }

        const files = fs.readdirSync(EXTRACTED_SCRIPTS_DIR)
            .filter(file => file.endsWith('.js') && file !== 'index.js')
            .sort();

        this.logger.info(`Found ${files.length} extracted scripts`);
        return files;
    }

    // Execute a single script
    async executeScript(scriptName) {
        const scriptPath = path.join(EXTRACTED_SCRIPTS_DIR, scriptName);
        
        if (!fs.existsSync(scriptPath)) {
            this.logger.error(`Script not found: ${scriptName}`);
            return false;
        }

        if (this.loadedScripts.has(scriptName)) {
            this.logger.info(`Script already loaded: ${scriptName}`);
            return true;
        }

        try {
            this.logger.info(`Executing script: ${scriptName}`);
            
            // Read script content
            const scriptContent = fs.readFileSync(scriptPath, 'utf8');
            
            // Create a sandbox context for the script
            const sandbox = {
                ...global,
                require: require,
                module: { exports: {} },
                exports: {},
                __filename: scriptPath,
                __dirname: path.dirname(scriptPath),
                console: {
                    log: (...args) => this.logger.info(args.join(' '), scriptName),
                    warn: (...args) => this.logger.warn(args.join(' '), scriptName),
                    error: (...args) => this.logger.error(args.join(' '), scriptName),
                    info: (...args) => this.logger.info(args.join(' '), scriptName)
                }
            };

            // Execute the script
            const vm = require('vm');
            const context = vm.createContext(sandbox);
            vm.runInContext(scriptContent, context, { filename: scriptName });

            this.loadedScripts.add(scriptName);
            this.scriptResults.set(scriptName, { success: true, exports: sandbox.module.exports });
            
            this.logger.info(`Successfully executed: ${scriptName}`);
            return true;

        } catch (error) {
            this.logger.error(`Failed to execute ${scriptName}: ${error.message}`);
            this.scriptResults.set(scriptName, { success: false, error: error.message });
            return false;
        }
    }

    // Execute all scripts
    async executeAllScripts() {
        const scripts = this.getExtractedScripts();
        
        if (scripts.length === 0) {
            this.logger.warn('No scripts found to execute');
            return;
        }

        this.logger.info(`Starting execution of ${scripts.length} scripts`);
        
        let successCount = 0;
        let failureCount = 0;

        for (const script of scripts) {
            const success = await this.executeScript(script);
            if (success) {
                successCount++;
            } else {
                failureCount++;
            }
        }

        this.logger.info(`Execution completed: ${successCount} successful, ${failureCount} failed`);
        this.generateReport();
    }

    // Execute specific scripts by pattern
    async executeScriptsByPattern(pattern) {
        const scripts = this.getExtractedScripts().filter(script => 
            script.includes(pattern) || script.match(new RegExp(pattern, 'i'))
        );

        if (scripts.length === 0) {
            this.logger.warn(`No scripts found matching pattern: ${pattern}`);
            return;
        }

        this.logger.info(`Executing ${scripts.length} scripts matching pattern: ${pattern}`);
        
        for (const script of scripts) {
            await this.executeScript(script);
        }
    }

    // Generate execution report
    generateReport() {
        const report = {
            timestamp: new Date().toISOString(),
            totalScripts: this.scriptResults.size,
            successful: Array.from(this.scriptResults.entries()).filter(([_, result]) => result.success).length,
            failed: Array.from(this.scriptResults.entries()).filter(([_, result]) => !result.success).length,
            results: Object.fromEntries(this.scriptResults)
        };

        const reportPath = path.join(__dirname, 'backend-execution-report.json');
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        
        this.logger.info(`Execution report saved to: ${reportPath}`);
        
        // Print summary
        console.log('\n=== EXECUTION SUMMARY ===');
        console.log(`Total scripts: ${report.totalScripts}`);
        console.log(`Successful: ${report.successful}`);
        console.log(`Failed: ${report.failed}`);
        console.log(`Report saved to: ${reportPath}`);
        console.log(`Detailed log: ${LOG_FILE}`);
    }
}

// CLI interface
async function main() {
    const args = process.argv.slice(2);
    const runner = new BackendScriptRunner();

    if (args.length === 0) {
        console.log('Backend Script Runner');
        console.log('Usage:');
        console.log('  node backend-script-runner.js all                    - Execute all scripts');
        console.log('  node backend-script-runner.js pattern <pattern>      - Execute scripts matching pattern');
        console.log('  node backend-script-runner.js single <script-name>   - Execute single script');
        console.log('  node backend-script-runner.js list                   - List all available scripts');
        return;
    }

    const command = args[0];

    switch (command) {
        case 'all':
            await runner.executeAllScripts();
            break;
        
        case 'pattern':
            if (args.length < 2) {
                console.error('Pattern required. Usage: node backend-script-runner.js pattern <pattern>');
                return;
            }
            await runner.executeScriptsByPattern(args[1]);
            break;
        
        case 'single':
            if (args.length < 2) {
                console.error('Script name required. Usage: node backend-script-runner.js single <script-name>');
                return;
            }
            await runner.executeScript(args[1]);
            break;
        
        case 'list':
            const scripts = runner.getExtractedScripts();
            console.log('Available extracted scripts:');
            scripts.forEach((script, index) => {
                console.log(`  ${index + 1}. ${script}`);
            });
            break;
        
        default:
            console.error(`Unknown command: ${command}`);
            console.log('Use "node backend-script-runner.js" for usage information');
    }
}

// Run if called directly
if (require.main === module) {
    main().catch(error => {
        console.error('Fatal error:', error);
        process.exit(1);
    });
}

module.exports = BackendScriptRunner;
