﻿/**
 * Extracted from: grind.html
 * Generated: 2025-06-09 13:05:31
 * Type: ES6 Module
 */

// Backend compatibility layer
if (typeof window === 'undefined') {
    global.window = global.window || {};
    global.document = global.document || {
        addEventListener: function() {},
        getElementById: function() { return { style: {}, innerHTML: '', textContent: '' }; },
        createElement: function() { return { style: {}, setAttribute: function() {}, appendChild: function() {} }; },
        querySelector: function() { return null; },
        querySelectorAll: function() { return []; },
        body: { appendChild: function() {}, style: {} },
        head: { appendChild: function() {} }
    };
    global.console = console;
}

// Original script content:
// Import Firebase modules
  import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
  import { getFirestore } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';
  import { getAuth } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';
  import { firebaseConfig } from './js/firebaseConfig.js';

  // Initialize Firebase safely
  let app;
  try {
    app = initializeApp(firebaseConfig);
    console.log("Firebase initialized via compatibility layer");
  } catch (e) {
    if (e.code === 'app/duplicate-app') {
      console.log("Firebase already initialized, using existing app");
      // Assuming the existing app can be retrieved this way if needed,
      // but the goal is usually just to not crash.
      // The getOrCreateFirebaseApp function handles this better,
      // but this script runs directly in HTML.
      // Let's try getting the default app instance if initialization failed due to duplicate.
      try {
         // Need to import firebase/app again to get the app() function if using v9 modular SDK style globally
         // Or rely on the fact that initializeApp might return the existing app in some versions/scenarios.
         // For simplicity here, we'll assume `app` holds the instance if it existed.
         // If not, we might need a more complex check like `firebase.app()` if using v8 compat.
         // Given the context, let's assume the catch block just prevents re-initialization error.
         // We'll rely on the fact that `getFirestore` and `getAuth` below might work with the implicitly existing app.
         // If this proves problematic, we might need to import `getApp` from firebase/app.
         app = initializeApp(); // Attempt to get existing app
      } catch(getAppError) {
         console.error("Could not get existing Firebase app instance.", getAppError);
      }
    } else {
      console.error("Firebase initialization error:", e);
    }
  }

  // Create window.firebase compatibility object for older scripts
  if (!window.firebase) {
    window.firebase = {
      apps: app ? [app] : [], // Include app if successfully initialized/retrieved
      initializeApp: (config) => {
        console.warn("Firebase already initialized via module script. Returning existing app.");
        // Return the existing app instance if available
        return app || initializeApp(config); // Fallback to re-init if app is null, though ideally caught above
      },
      // Ensure firestore and auth are initialized only if app exists
      firestore: () => app ? getFirestore(app) : null,
      auth: () => app ? getAuth(app) : null
    };
    // Add db to window for cross-compatibility if app exists
    if (app) {
       window.db = getFirestore(app);
       window.auth = getAuth(app); // Explicitly set window.auth here
    } else {
       console.error("Firebase app instance is not available. Cannot set window.db or window.auth.");
    }
  } else {
     // If window.firebase already exists, ensure it has the necessary methods
     if (!window.firebase.firestore && app) window.firebase.firestore = () => getFirestore(app);
     if (!window.firebase.auth && app) window.firebase.auth = () => getAuth(app);
     if (!window.auth && app) window.auth = getAuth(app); // Also ensure window.auth is set
     if (!window.db && app) window.db = getFirestore(app); // Ensure db is set even if window.firebase existed
  }
