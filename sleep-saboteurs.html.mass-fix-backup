<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPAce - Sleep Saboteurs</title>
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="assets/images/gpace-logo-white.png">
    <link rel="shortcut icon" type="image/png" href="assets/images/gpace-logo-white.png">

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Side Drawer Styles -->
    <link href="css/sideDrawer.css" rel="stylesheet">
    <link href="css/alarm-service.css" rel="stylesheet">
    <link href="css/sleep-saboteurs.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation Bar -->
    <nav class="top-nav">
        <div class="nav-brand d-flex align-items-center">
            <img
                src="assets/images/gpace-logo-white.png"
                alt="GPAce Logo"
                style="height: 60px; margin-right: 0px;"
            >
            <a href="grind.html" style="text-decoration: none; color: inherit;">GPAce</a>
        </div>
        <div class="nav-links">
            <a href="grind.html">Grind Mode</a>
            <a href="study-spaces.html">Grind Station</a>
            <a href="daily-calendar.html">Daily Drip</a>
            <a href="academic-details.html">Brain Juice</a>
            <a href="extracted.html">Hustle Hub</a>
            <a href="subject-marks.html">Subject Marks</a>
            <a href="sleep-saboteurs.html" class="active">Sleep Saboteurs</a>
        </div>
    </nav>

    <!-- Time Format Toggle -->
    <button class="time-format-toggle">
        <i class="bi bi-clock"></i>
        <span class="format-text">24hr</span>
    </button>

    <!-- Main Container -->
    <div class="container mt-5">
        <div class="alarm-container">
            <!-- Clock Display -->
            <div class="clock">
                <span class="hour">00</span>
                <span class="colon">:</span>
                <span class="minute">00</span>
                <span class="colon">:</span>
                <div class="side">
                    <span class="second">00</span>
                    <span class="am-pm">AM</span>
                </div>
            </div>

            <!-- Date Display -->
            <div class="days">
                <span class="month">Jan</span>
                <span class="day">Monday</span>
                <span class="date">01</span>
            </div>

            <!-- Add Alarm Button -->
            <button id="addAlarmBtn" class="btn alarm-button">
                <i class="bi bi-plus-circle-fill me-2"></i>Add Alarm
            </button>

            <!-- Alarm Form -->
            <div class="alarm-form">
                <label>Set Alarm Time</label>
                <div class="time-inputs">
                    <input type="number" id="hour" min="1" max="12" placeholder="Hour" required>
                    <input type="number" id="minute" min="0" max="59" placeholder="Min" required>
                    <input type="number" id="second" min="0" max="59" placeholder="Sec" required>
                </div>

                <div class="am-pm-toggle">
                    <button type="button" class="am-btn active">AM</button>
                    <button type="button" class="pm-btn">PM</button>
                </div>

                <div class="label-input">
                    <label>Alarm Label</label>
                    <input type="text" id="alarm-label" placeholder="Enter alarm label">
                </div>

                <div class="alarm-actions">
                    <button type="button" class="set-alarm-btn">Set Alarm</button>
                    <button type="button" class="cancel-alarm-btn">Cancel</button>
                </div>
            </div>

            <!-- Alarm List -->
            <div class="alarm-list" id="alarm-list">
                <!-- Alarms will be added here dynamically -->
            </div>
        </div>
    </div>

    <!-- Wave Animation Container -->
    <div class="wave-container">
        <div class="wave"></div>
        <div class="wave"></div>
        <div class="wave"></div>
    </div>

    <!-- Keyboard Shortcuts Tooltip -->
    <div class="keyboard-shortcut">
        <span><kbd>Alt</kbd> + <kbd>A</kbd> Quick Add</span>
        <span><kbd>Alt</kbd> + <kbd>T</kbd> Templates</span>
        <span><kbd>Alt</kbd> + <kbd>B</kbd> Bulk Add</span>
    </div>

    <!-- Audio Elements -->
    <audio id="alarm-sound" preload="auto">
        <source src="alarm-sounds/alexa-ringtone.mp3" type="audio/mpeg">
        Your browser does not support the audio element.
    </audio>

    <!-- Load modularized JavaScript files -->






</body>
</html>






</body>
</html>
    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Firebase Initialization -->
    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getFirestore } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';
        import { getAuth } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';
        import { firebaseConfig } from './js/firebaseConfig.js';

        // Initialize Firebase safely
        let app;
        try {
            app = initializeApp(firebaseConfig);
            console.log("Firebase initialized successfully");

            // Set up global variables
            window.db = getFirestore(app);
            window.auth = getAuth(app);
        } catch (e) {
            if (e.code === 'app/duplicate-app') {
                console.log("Firebase already initialized, using existing app");
                try {
                    app = initializeApp();
                    window.db = getFirestore(app);
                    window.auth = getAuth(app);
                } catch(getAppError) {
                    console.error("Could not get existing Firebase app instance.", getAppError);
                }
            } else {
                console.error("Firebase initialization error:", e);
            }
        }
    </script>

    <!-- Authentication Setup -->
    <script type="module">
        import { auth as importedAuth, signInWithGoogle, signOutUser, initializeAuth } from './js/auth.js';
        window.auth = window.auth || importedAuth;
        window.signInWithGoogle = signInWithGoogle;
        window.signOutUser = signOutUser;

        // Initialize authentication
        document.addEventListener('DOMContentLoaded', () => {
            initializeAuth();
        });
    </script>

    <!-- Firestore Data Operations -->
    <script type="module">
        import { initializeFirestoreData } from './js/initFirestoreData.js';
        window.initializeFirestoreData = initializeFirestoreData;

        // Initialize data when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                if (typeof window.initializeFirestoreData === 'function') {
                    window.initializeFirestoreData();
                }
            }, 1500);
        });
    </script>

    <script src="js/alarm-service.js"></script>
    <script src="js/clock-display.js"></script>
    <script src="js/theme-manager.js"></script>
    <script src="js/sleep-saboteurs-init.js"></script>
    <script type="module" src="js/cross-tab-sync.js"></script>
    <script src="js/sideDrawer.js"></script>
    <script src="/js/inject-header.js"></script>
</body>
</html>