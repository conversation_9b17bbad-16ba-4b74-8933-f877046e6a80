# Script Modularization and Backend Execution

This documentation describes the complete process for extracting inline JavaScript from HTML files, modularizing them, and enabling backend execution.

## Overview

The script modularization system consists of several components that work together to:

1. **Extract** inline JavaScript code from HTML files
2. **Modularize** the extracted code into separate `.js` files
3. **Make scripts backend-compatible** by adding Node.js polyfills
4. **Test execution** in a backend environment
5. **Generate reports** on the extraction and execution process

## Files and Components

### Core Scripts

- **`extract-and-modularize-scripts.ps1`** - PowerShell script that performs the extraction
- **`backend-script-runner.js`** - Node.js script that can execute extracted scripts in backend
- **`complete-script-modularization.bat`** - Batch file that orchestrates the entire process

### Helper Files

- **`run-script-extraction.bat`** - Simple batch file to run just the extraction
- **`SCRIPT-MODULARIZATION-README.md`** - This documentation file

## Quick Start

### Option 1: Complete Process (Recommended)
```bash
# Run the complete modularization process
complete-script-modularization.bat
```

### Option 2: Step by Step

1. **Preview what will be extracted (dry run):**
   ```bash
   run-script-extraction.bat
   # Choose option 1 or 3
   ```

2. **Extract and modularize scripts:**
   ```bash
   run-script-extraction.bat
   # Choose option 2 or 4
   ```

3. **Test extracted scripts in backend:**
   ```bash
   node backend-script-runner.js all
   ```

## Detailed Process

### 1. Script Extraction

The PowerShell script `extract-and-modularize-scripts.ps1` performs the following:

- **Scans HTML files** for inline `<script>` tags (without `src` attribute)
- **Analyzes script content** to determine functionality and generate meaningful filenames
- **Creates backup copies** of all HTML files before modification
- **Extracts scripts** to `js/extracted/` directory
- **Replaces inline scripts** with external script references
- **Adds backend compatibility** layers for Node.js execution

#### Script Naming Convention

Extracted scripts are named based on their content and source:
- `{source-file}-{functionality}-{counter}.js`
- Examples:
  - `grind-firebase-1.js` - Firebase-related script from grind.html
  - `index-timer-2.js` - Timer-related script from index.html
  - `flashcards-events-1.module.js` - ES6 module with event handlers

#### Script Types Detected

- **Firebase/Auth** - Scripts containing Firebase or authentication code
- **Timer/Pomodoro** - Timer and countdown functionality
- **Modal/Dialog** - Modal and popup management
- **Visualization** - Charts, graphs, and visual components
- **Energy/Fatigue** - Energy tracking and hologram features
- **Tasks** - Task management and priority calculations
- **Events** - Event listeners and DOM manipulation
- **Imports** - ES6 import/export statements

### 2. Backend Compatibility

Each extracted script is enhanced with:

```javascript
// Backend compatibility layer
if (typeof window === 'undefined') {
    // Node.js environment - add polyfills if needed
    global.window = global.window || {};
    global.document = global.document || {
        addEventListener: function() {},
        getElementById: function() { return null; },
        // ... more polyfills
    };
}

// Original script content
// ... your extracted code here
```

### 3. Backend Execution

The `backend-script-runner.js` provides several execution modes:

#### Execute All Scripts
```bash
node backend-script-runner.js all
```

#### Execute Scripts by Pattern
```bash
node backend-script-runner.js pattern firebase
node backend-script-runner.js pattern timer
node backend-script-runner.js pattern grind
```

#### Execute Single Script
```bash
node backend-script-runner.js single grind-firebase-1.js
```

#### List Available Scripts
```bash
node backend-script-runner.js list
```

## Directory Structure

After running the modularization process:

```
project-root/
├── js/
│   ├── extracted/                    # Extracted and modularized scripts
│   │   ├── index.js                 # Index file for backend imports
│   │   ├── grind-firebase-1.js      # Extracted Firebase script
│   │   ├── grind-timer-2.js         # Extracted timer script
│   │   └── ...                      # Other extracted scripts
│   └── ...                          # Existing JS files
├── backup-before-extraction/         # Backup of original HTML files
│   ├── grind.html.backup-20241220-143022
│   └── ...
├── backend-execution.log             # Detailed execution log
├── backend-execution-report.json     # Summary report
└── ...
```

## Configuration and Customization

### HTML Files Processed

The system processes these HTML files by default:
- `index.html`
- `grind.html`
- `academic-details.html`
- `workspace.html`
- `extracted.html`
- `daily-calendar.html`
- `study-spaces.html`
- `subject-marks.html`
- `settings.html`
- `tasks.html`
- `landing.html`
- `sleep-saboteurs.html`
- `404.html`
- `priority-list.html`
- `priority-calculator.html`
- `flashcards.html`
- `instant-test-feedback.html`

### Adding New HTML Files

To process additional HTML files, edit the `$HtmlFiles` array in `extract-and-modularize-scripts.ps1`:

```powershell
$HtmlFiles = @(
    'index.html',
    'grind.html',
    # ... existing files
    'your-new-file.html'  # Add your file here
)
```

### Customizing Script Detection

Modify the `Get-ScriptName` function in the PowerShell script to add new functionality detection patterns:

```powershell
if ($Content -match 'your-pattern|your-keyword') {
    $functionality = "your-functionality"
}
```

## Troubleshooting

### Common Issues

1. **PowerShell Execution Policy**
   ```bash
   # If you get execution policy errors, run:
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
   ```

2. **Node.js Not Found**
   - Install Node.js from https://nodejs.org/
   - Ensure `node` is in your PATH

3. **No Scripts Extracted**
   - Check if HTML files contain inline `<script>` tags
   - Verify file paths are correct
   - Run with `-Verbose` flag for detailed output

4. **Backend Execution Errors**
   - Check `backend-execution.log` for detailed error messages
   - Some browser-specific APIs may need additional polyfills
   - Consider running scripts individually to isolate issues

### Logs and Reports

- **`backend-execution.log`** - Detailed execution log with timestamps
- **`backend-execution-report.json`** - JSON summary of execution results
- **Console output** - Real-time progress and status information

## Recovery and Rollback

### Restore Original Files
```bash
complete-script-modularization.bat
# Choose option 7 (Restore from backup)
```

### Clean Up Extracted Scripts
```bash
complete-script-modularization.bat
# Choose option 6 (Clean up)
```

## Advanced Usage

### Custom Backend Environment

You can extend the backend runner with custom polyfills:

```javascript
// In backend-script-runner.js, modify the global polyfills
global.customAPI = {
    // Your custom backend API
};
```

### Integration with Build Process

Add the extraction to your build pipeline:

```bash
# In your build script
powershell -ExecutionPolicy Bypass -File "extract-and-modularize-scripts.ps1"
node backend-script-runner.js all
```

## Benefits

1. **Separation of Concerns** - HTML and JavaScript are properly separated
2. **Backend Compatibility** - Scripts can run in Node.js environment
3. **Maintainability** - Easier to manage and update individual scripts
4. **Testing** - Scripts can be unit tested independently
5. **Performance** - Better caching and loading strategies
6. **Code Reuse** - Extracted scripts can be imported in other projects

## Next Steps

After successful modularization:

1. **Review extracted scripts** for optimization opportunities
2. **Consolidate similar functionality** into shared modules
3. **Add unit tests** for critical scripts
4. **Set up continuous integration** to run backend tests
5. **Consider bundling** for production deployment
