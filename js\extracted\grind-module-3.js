﻿/**
 * Extracted from: grind.html
 * Generated: 2025-06-09 13:05:31
 * Type: ES6 Module
 */

// Backend compatibility layer
if (typeof window === 'undefined') {
    global.window = global.window || {};
    global.document = global.document || {
        addEventListener: function() {},
        getElementById: function() { return { style: {}, innerHTML: '', textContent: '' }; },
        createElement: function() { return { style: {}, setAttribute: function() {}, appendChild: function() {} }; },
        querySelector: function() { return null; },
        querySelectorAll: function() { return []; },
        body: { appendChild: function() {}, style: {} },
        head: { appendChild: function() {} }
    };
    global.console = console;
}

// Original script content:
import dataSyncManager from './js/data-sync-manager.js';
    // Initialize data sync after Firebase initialization
    document.addEventListener('DOMContentLoaded', async () => {
      // Wait for auth to be ready
      await new Promise(resolve => {
        const checkAuth = setInterval(() => {
          if (window.auth) {
            clearInterval(checkAuth);
            resolve();
          }
        }, 100);
      });
      // Initialize data sync
      await dataSyncManager.initializeDataSync(true);
      // Start periodic sync
      dataSyncManager.startPeriodicSync();
    });
    // Listen for sync completion
    window.addEventListener('dataSyncComplete', (event) => {
      console.log('🔄 Data sync completed at:', new Date(event.detail.timestamp).toLocaleString());
      // Refresh priority task display only if needed
      if (typeof displayPriorityTask === 'function') {
        // Check if tasks have changed before refreshing
        const tasksJson = localStorage.getItem('calculatedPriorityTasks') || '[]';
        const currentHash = hashString(tasksJson);
        if (currentHash !== lastTasksHash) {
          console.log('Tasks changed during sync, refreshing display');
          displayPriorityTask();
        } else {
          console.log('Tasks unchanged during sync, skipping refresh');
        }
      }
    });
