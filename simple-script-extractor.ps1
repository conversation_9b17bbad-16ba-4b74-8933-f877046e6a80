# Simple Script Extractor - PowerShell Script
# Extracts inline JavaScript from HTML files and creates modular JS files

param(
    [switch]$DryRun = $false
)

# Configuration
$ScriptRoot = $PSScriptRoot
$ExtractedDir = Join-Path $ScriptRoot "js\extracted"
$BackupDir = Join-Path $ScriptRoot "backup-before-extraction"

# Create directories
if (-not (Test-Path $ExtractedDir)) {
    New-Item -ItemType Directory -Path $ExtractedDir -Force | Out-Null
}
if (-not (Test-Path $BackupDir)) {
    New-Item -ItemType Directory -Path $BackupDir -Force | Out-Null
}

# HTML files to process
$HtmlFiles = @(
    'index.html', 'grind.html', 'academic-details.html', 'workspace.html',
    'extracted.html', 'daily-calendar.html', 'study-spaces.html', 
    'subject-marks.html', 'settings.html', 'tasks.html', 'landing.html',
    'sleep-saboteurs.html', '404.html', 'priority-list.html', 
    'priority-calculator.html', 'flashcards.html', 'instant-test-feedback.html'
)

Write-Host "Starting script extraction..." -ForegroundColor Green
Write-Host "Dry run mode: $DryRun" -ForegroundColor $(if ($DryRun) { "Yellow" } else { "Green" })

$totalExtracted = 0

foreach ($htmlFile in $HtmlFiles) {
    $filePath = Join-Path $ScriptRoot $htmlFile
    
    if (-not (Test-Path $filePath)) {
        continue
    }
    
    Write-Host "Processing: $htmlFile" -ForegroundColor Cyan
    
    # Create backup
    if (-not $DryRun) {
        $backupPath = Join-Path $BackupDir "$htmlFile.backup-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
        Copy-Item $filePath $backupPath
        Write-Host "  Backed up to: $backupPath" -ForegroundColor Gray
    }
    
    # Read HTML content
    $content = Get-Content $filePath -Raw -Encoding UTF8
    
    # Find inline scripts (scripts without src attribute)
    $scriptPattern = '(?s)<script(?![^>]*\bsrc\s*=)([^>]*)>(.*?)</script>'
    $matches = [regex]::Matches($content, $scriptPattern, [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
    
    $scriptCount = 0
    $modifiedContent = $content
    
    foreach ($match in $matches) {
        $attributes = $match.Groups[1].Value.Trim()
        $scriptContent = $match.Groups[2].Value.Trim()
        
        # Skip empty scripts
        if ([string]::IsNullOrWhiteSpace($scriptContent)) {
            continue
        }
        
        $scriptCount++
        $totalExtracted++
        
        # Generate filename
        $baseName = [System.IO.Path]::GetFileNameWithoutExtension($htmlFile)
        $scriptName = "$baseName-script-$scriptCount.js"
        
        # Determine if it's a module
        $isModule = $attributes -match 'type.*module'
        if ($isModule) {
            $scriptName = "$baseName-module-$scriptCount.js"
        }
        
        Write-Host "  Extracting: $scriptName" -ForegroundColor Yellow
        
        if (-not $DryRun) {
            # Create script file with backend compatibility
            $scriptPath = Join-Path $ExtractedDir $scriptName
            
            $header = @"
/**
 * Extracted from: $htmlFile
 * Generated: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
 * Type: $(if ($isModule) { 'ES6 Module' } else { 'Regular Script' })
 */

// Backend compatibility layer
if (typeof window === 'undefined') {
    global.window = global.window || {};
    global.document = global.document || {
        addEventListener: function() {},
        getElementById: function() { return { style: {}, innerHTML: '', textContent: '' }; },
        createElement: function() { return { style: {}, setAttribute: function() {}, appendChild: function() {} }; },
        querySelector: function() { return null; },
        querySelectorAll: function() { return []; },
        body: { appendChild: function() {}, style: {} },
        head: { appendChild: function() {} }
    };
    global.console = console;
}

// Original script content:
$scriptContent
"@
            
            Set-Content -Path $scriptPath -Value $header -Encoding UTF8
        }
        
        # Replace in HTML
        $replacement = if ($isModule) {
            "<script type=`"module`" src=`"js/extracted/$scriptName`"></script>"
        } else {
            "<script src=`"js/extracted/$scriptName`"></script>"
        }
        
        $modifiedContent = $modifiedContent -replace [regex]::Escape($match.Value), $replacement
    }
    
    if ($scriptCount -gt 0) {
        Write-Host "  Extracted $scriptCount script(s)" -ForegroundColor Green
        
        # Save modified HTML
        if (-not $DryRun) {
            Set-Content -Path $filePath -Value $modifiedContent -Encoding UTF8
            Write-Host "  Updated HTML file" -ForegroundColor Green
        }
    } else {
        Write-Host "  No inline scripts found" -ForegroundColor Gray
    }
}

Write-Host "`nExtraction Summary:" -ForegroundColor Magenta
Write-Host "Total scripts extracted: $totalExtracted" -ForegroundColor White
Write-Host "Scripts location: $ExtractedDir" -ForegroundColor White
Write-Host "Backups location: $BackupDir" -ForegroundColor White

if ($DryRun) {
    Write-Host "`nThis was a dry run. No files were modified." -ForegroundColor Yellow
} else {
    Write-Host "`nExtraction completed successfully!" -ForegroundColor Green
}

# Create index file for backend use
if (-not $DryRun -and $totalExtracted -gt 0) {
    $indexPath = Join-Path $ExtractedDir "index.js"
    $indexContent = @"
/**
 * Index file for extracted scripts
 * Generated: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
 */

// This file can be used to import all extracted scripts in Node.js
console.log('Loading extracted scripts...');

// Add your script imports here as needed
module.exports = {
    // Export functionality as needed
};
"@
    
    Set-Content -Path $indexPath -Value $indexContent -Encoding UTF8
    Write-Host "Created index file: $indexPath" -ForegroundColor Cyan
}
