﻿/**
 * Extracted from: grind.html
 * Generated: 2025-06-09 13:05:32
 * Type: Regular Script
 */

// Backend compatibility layer
if (typeof window === 'undefined') {
    global.window = global.window || {};
    global.document = global.document || {
        addEventListener: function() {},
        getElementById: function() { return { style: {}, innerHTML: '', textContent: '' }; },
        createElement: function() { return { style: {}, setAttribute: function() {}, appendChild: function() {} }; },
        querySelector: function() { return null; },
        querySelectorAll: function() { return []; },
        body: { appendChild: function() {}, style: {} },
        head: { appendChild: function() {} }
    };
    global.console = console;
}

// Original script content:
// This script runs at the end of the body and continuously checks and updates the title
  // to ensure it reflects the timer state correctly

  // Run the title enforcer every 500ms
  setInterval(() => {
    // Check if the timer is running
    if (typeof state !== 'undefined' && state.isRunning && state.timerInterval) {
      // Calculate minutes and seconds
      const minutes = Math.floor(state.timeLeft / 60);
      const seconds = state.timeLeft % 60;
      // Format the display
      const display = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
      // Get the timer type
      const timerType = state.currentState === TIMER_STATES.FOCUS ? 'Focus' : 'Break';
      // Update the document title
      document.title = `${display} - ${timerType} - GPAce`;
      console.log('Title enforcer: Updated title with timer info', display);
    }
  }, 500);
