# Fix multiple body tags in HTML files
param(
    [string]$FilePath = "grind.html",
    [switch]$WhatIf = $false
)

Write-Host "============================================" -ForegroundColor Magenta
Write-Host "  HTML BODY TAG FIXER" -ForegroundColor Magenta
Write-Host "============================================" -ForegroundColor Magenta
Write-Host ""

if ($WhatIf) {
    Write-Host "WHAT-IF MODE: No files will be modified" -ForegroundColor Yellow
    Write-Host ""
}

Write-Host "Processing: $FilePath" -ForegroundColor Cyan

if (-not (Test-Path $FilePath)) {
    Write-Host "File not found: $FilePath" -ForegroundColor Red
    exit 1
}

# Read the file
$content = Get-Content $FilePath -Raw -Encoding UTF8

# Count closing body tags
$bodyCloseTags = [regex]::Matches($content, '</body>', [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
Write-Host "Found $($bodyCloseTags.Count) closing body tags" -ForegroundColor Yellow

if ($bodyCloseTags.Count -le 1) {
    Write-Host "✓ No issues found - file has correct number of body tags" -ForegroundColor Green
    exit 0
}

Write-Host "ISSUE: Multiple closing body tags found!" -ForegroundColor Red

# Show line numbers where they occur
$lines = $content -split "`n"
$bodyTagLines = @()
for ($i = 0; $i -lt $lines.Count; $i++) {
    if ($lines[$i] -match '</body>') {
        $bodyTagLines += $i + 1
        Write-Host "  Line $($i + 1): $($lines[$i].Trim())" -ForegroundColor Yellow
    }
}

if ($WhatIf) {
    Write-Host ""
    Write-Host "[WHAT-IF] Would remove duplicate closing body tags from lines:" -ForegroundColor Cyan
    for ($i = 0; $i -lt ($bodyTagLines.Count - 1); $i++) {
        Write-Host "  - Line $($bodyTagLines[$i])" -ForegroundColor Yellow
    }
    Write-Host "[WHAT-IF] Would keep the last closing body tag at line $($bodyTagLines[$bodyTagLines.Count - 1])" -ForegroundColor Green
    exit 0
}

# Create backup
$backupPath = "$FilePath.body-fix-backup"
Copy-Item $FilePath $backupPath -Force
Write-Host "Backup created: $backupPath" -ForegroundColor Cyan

# Fix the issue by removing all but the last closing body tag
$fixedContent = $content

# Remove duplicate closing body tags (keep only the last one)
# We'll work backwards to avoid index shifting issues
for ($i = $bodyCloseTags.Count - 2; $i -ge 0; $i--) {
    $match = $bodyCloseTags[$i]
    $lineStart = $match.Index
    
    # Find the start of the line containing this tag
    $beforeMatch = $fixedContent.Substring(0, $lineStart)
    $lastNewline = $beforeMatch.LastIndexOf("`n")
    if ($lastNewline -eq -1) { $lastNewline = 0 } else { $lastNewline++ }
    
    # Find the end of the line containing this tag
    $afterMatch = $fixedContent.Substring($match.Index + $match.Length)
    $nextNewline = $afterMatch.IndexOf("`n")
    if ($nextNewline -eq -1) { $nextNewline = $afterMatch.Length } else { $nextNewline++ }
    
    # Remove the entire line containing the duplicate body tag
    $lineEnd = $match.Index + $match.Length + $nextNewline
    $fixedContent = $fixedContent.Remove($lastNewline, $lineEnd - $lastNewline)
    
    Write-Host "Removed duplicate closing body tag from line $($bodyTagLines[$i])" -ForegroundColor Green
}

# Write the fixed content back to the file
$fixedContent | Out-File -FilePath $FilePath -Encoding UTF8 -NoNewline

Write-Host ""
Write-Host "✓ Successfully fixed HTML structure!" -ForegroundColor Green
Write-Host "Removed $($bodyCloseTags.Count - 1) duplicate closing body tags" -ForegroundColor Green
Write-Host "Original file backed up as: $backupPath" -ForegroundColor Cyan

# Verify the fix
$verifyContent = Get-Content $FilePath -Raw -Encoding UTF8
$verifyBodyTags = [regex]::Matches($verifyContent, '</body>', [System.Text.RegularExpressions.RegexOptions]::IgnoreCase)
Write-Host ""
Write-Host "Verification: File now has $($verifyBodyTags.Count) closing body tag(s)" -ForegroundColor $(if ($verifyBodyTags.Count -eq 1) { "Green" } else { "Red" })

if ($verifyBodyTags.Count -eq 1) {
    Write-Host "🎉 HTML structure is now valid!" -ForegroundColor Green
} else {
    Write-Host "⚠️  Something went wrong - please check the file manually" -ForegroundColor Red
}

Write-Host ""
Write-Host "Fix completed at $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Cyan
