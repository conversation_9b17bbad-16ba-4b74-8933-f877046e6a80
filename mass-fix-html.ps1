# Mass fix HTML structure issues
$filesToFix = @(
    'grind.html', 'extracted.html', 'subject-marks.html', 'settings.html',
    'landing.html', 'sleep-saboteurs.html', '404.html', 'priority-list.html', 'priority-calculator.html'
)

Write-Host "============================================" -ForegroundColor Magenta
Write-Host "  MASS HTML STRUCTURE FIXER" -ForegroundColor Magenta
Write-Host "============================================" -ForegroundColor Magenta
Write-Host ""

$totalFixed = 0

foreach ($file in $filesToFix) {
    if (-not (Test-Path $file)) {
        continue
    }
    
    Write-Host "Processing: $file" -ForegroundColor Cyan
    
    # Create backup
    $backupPath = "$file.mass-fix-backup"
    Copy-Item $file $backupPath -Force
    Write-Host "  Backup: $backupPath" -ForegroundColor Green
    
    # Read and process file
    $content = Get-Content $file -Raw -Encoding UTF8
    $lines = $content -split "`n"
    $modified = $false
    
    # Fix closing body tags - keep only the last one
    $bodyLines = @()
    for ($i = 0; $i -lt $lines.Count; $i++) {
        if ($lines[$i] -match '</body>') {
            $bodyLines += $i
        }
    }
    
    if ($bodyLines.Count -gt 1) {
        Write-Host "  Fixing $($bodyLines.Count) body tags..." -ForegroundColor Yellow
        for ($i = 0; $i -lt ($bodyLines.Count - 1); $i++) {
            $lines[$bodyLines[$i]] = ""
        }
        $modified = $true
    }
    
    # Fix closing html tags - keep only the last one
    $htmlLines = @()
    for ($i = 0; $i -lt $lines.Count; $i++) {
        if ($lines[$i] -match '</html>') {
            $htmlLines += $i
        }
    }
    
    if ($htmlLines.Count -gt 1) {
        Write-Host "  Fixing $($htmlLines.Count) html tags..." -ForegroundColor Yellow
        for ($i = 0; $i -lt ($htmlLines.Count - 1); $i++) {
            $lines[$htmlLines[$i]] = ""
        }
        $modified = $true
    }
    
    # Save if modified
    if ($modified) {
        $fixedContent = ($lines -join "`n")
        $fixedContent = $fixedContent -replace "`n`n`n+", "`n`n"
        $fixedContent | Out-File -FilePath $file -Encoding UTF8 -NoNewline
        Write-Host "  FIXED: $file" -ForegroundColor Green
        $totalFixed++
    } else {
        Write-Host "  OK: No issues found" -ForegroundColor Cyan
    }
    
    Write-Host ""
}

Write-Host "============================================" -ForegroundColor Magenta
Write-Host "SUMMARY: Fixed $totalFixed files" -ForegroundColor Green
Write-Host "All files backed up with .mass-fix-backup extension" -ForegroundColor Cyan
Write-Host "============================================" -ForegroundColor Magenta
