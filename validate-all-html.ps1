# Comprehensive HTML validation for all files
param([switch]$Fix = $false)

$htmlFiles = @(
    'index.html',
    'grind.html',
    'academic-details.html',
    'workspace.html',
    'extracted.html',
    'daily-calendar.html',
    'study-spaces.html',
    'subject-marks.html',
    'settings.html',
    'tasks.html',
    'landing.html',
    'sleep-saboteurs.html',
    '404.html',
    'priority-list.html',
    'priority-calculator.html',
    'flashcards.html',
    'instant-test-feedback.html'
)

Write-Host "============================================" -ForegroundColor Magenta
Write-Host "  COMPREHENSIVE HTML VALIDATION" -ForegroundColor Magenta
Write-Host "============================================" -ForegroundColor Magenta
Write-Host ""

if ($Fix) {
    Write-Host "FIX MODE: Issues will be automatically corrected" -ForegroundColor Yellow
} else {
    Write-Host "VALIDATION MODE: Issues will be reported only" -ForegroundColor Cyan
}
Write-Host ""

$totalFiles = 0
$filesWithIssues = 0
$issuesFound = 0
$issuesFixed = 0

foreach ($file in $htmlFiles) {
    if (-not (Test-Path $file)) {
        Write-Host "⚠️  File not found: $file" -ForegroundColor Yellow
        continue
    }
    
    $totalFiles++
    Write-Host "Checking: $file" -ForegroundColor Cyan
    
    $content = Get-Content $file -Raw -Encoding UTF8
    $fileIssues = @()
    
    # Check closing body tags
    $bodyCloseTags = ($content | Select-String -Pattern '</body>' -AllMatches).Matches.Count
    if ($bodyCloseTags -gt 1) {
        $fileIssues += "Multiple closing body tags ($bodyCloseTags found)"
        $issuesFound++
    } elseif ($bodyCloseTags -eq 0) {
        $fileIssues += "Missing closing body tag"
        $issuesFound++
    }
    
    # Check opening body tags
    $bodyOpenTags = ($content | Select-String -Pattern '<body[^>]*>' -AllMatches).Matches.Count
    if ($bodyOpenTags -gt 1) {
        $fileIssues += "Multiple opening body tags ($bodyOpenTags found)"
        $issuesFound++
    } elseif ($bodyOpenTags -eq 0) {
        $fileIssues += "Missing opening body tag"
        $issuesFound++
    }
    
    # Check closing html tags
    $htmlCloseTags = ($content | Select-String -Pattern '</html>' -AllMatches).Matches.Count
    if ($htmlCloseTags -gt 1) {
        $fileIssues += "Multiple closing html tags ($htmlCloseTags found)"
        $issuesFound++
    } elseif ($htmlCloseTags -eq 0) {
        $fileIssues += "Missing closing html tag"
        $issuesFound++
    }
    
    # Check opening html tags
    $htmlOpenTags = ($content | Select-String -Pattern '<html[^>]*>' -AllMatches).Matches.Count
    if ($htmlOpenTags -gt 1) {
        $fileIssues += "Multiple opening html tags ($htmlOpenTags found)"
        $issuesFound++
    }
    
    # Check script tag balance
    $scriptOpenTags = ($content | Select-String -Pattern '<script[^>]*>' -AllMatches).Matches.Count
    $scriptCloseTags = ($content | Select-String -Pattern '</script>' -AllMatches).Matches.Count
    if ($scriptOpenTags -ne $scriptCloseTags) {
        $fileIssues += "Mismatched script tags ($scriptOpenTags opening vs $scriptCloseTags closing)"
        $issuesFound++
    }
    
    # Report issues
    if ($fileIssues.Count -gt 0) {
        $filesWithIssues++
        Write-Host "  ❌ Found $($fileIssues.Count) issue(s):" -ForegroundColor Red
        foreach ($issue in $fileIssues) {
            Write-Host "    - $issue" -ForegroundColor Yellow
        }
        
        # Fix issues if requested
        if ($Fix) {
            $fixed = $false
            
            # Fix multiple closing body tags
            if ($bodyCloseTags -gt 1) {
                Write-Host "    🔧 Fixing multiple closing body tags..." -ForegroundColor Cyan
                & powershell -ExecutionPolicy Bypass -File "simple-body-fix.ps1" -FilePath $file
                $fixed = $true
                $issuesFixed++
            }
            
            if ($fixed) {
                Write-Host "    ✅ Issues fixed for $file" -ForegroundColor Green
            }
        }
    } else {
        Write-Host "  ✅ No issues found" -ForegroundColor Green
    }
    
    Write-Host ""
}

# Summary
Write-Host "============================================" -ForegroundColor Magenta
Write-Host "  VALIDATION SUMMARY" -ForegroundColor Magenta
Write-Host "============================================" -ForegroundColor Magenta

Write-Host "Files checked: $totalFiles" -ForegroundColor Cyan
Write-Host "Files with issues: $filesWithIssues" -ForegroundColor $(if ($filesWithIssues -eq 0) { "Green" } else { "Yellow" })
Write-Host "Total issues found: $issuesFound" -ForegroundColor $(if ($issuesFound -eq 0) { "Green" } else { "Red" })

if ($Fix) {
    Write-Host "Issues fixed: $issuesFixed" -ForegroundColor Green
}

if ($issuesFound -eq 0) {
    Write-Host ""
    Write-Host "🎉 All HTML files are structurally valid!" -ForegroundColor Green
} elseif ($Fix -and $issuesFixed -gt 0) {
    Write-Host ""
    Write-Host "🔧 Issues have been automatically fixed!" -ForegroundColor Green
    Write-Host "Original files are backed up with .body-fix-backup extension" -ForegroundColor Cyan
} elseif (-not $Fix) {
    Write-Host ""
    Write-Host "⚠️  Issues found! Run with -Fix to automatically correct them:" -ForegroundColor Yellow
    Write-Host "  .\validate-all-html.ps1 -Fix" -ForegroundColor Cyan
}

Write-Host ""
Write-Host "Validation completed at $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')" -ForegroundColor Cyan
